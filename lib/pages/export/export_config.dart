import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/utils/common_utils.dart';
import 'package:mooeli/widget/my_widget.dart';

showExportConfirmDialog(String title, String message,
    {double height = 300,
    VoidCallback? onConfirm,
    String confirmText = '',
    String cancelText = ''}) {
  const String tag = "showExportConfirmDialog";
  showCustomDialog(
      Container(
        width: 520.sp,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.sp),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(vertical: 10.sp, horizontal: 24.sp),
              child: Row(
                children: [
                  Text(title,
                      style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.w600,
                          color: color2B)),
                  const Spacer(),
                  Click(
                      onTap: () {
                        SmartDialog.dismiss(tag: tag);
                      },
                      child: Image.asset(
                        "res/icons/icon_close_purple.png",
                        width: 24.sp,
                        height: 24.sp,
                        color: color2B,
                      )),
                ],
              ),
            ),
            Divider(
              thickness: 1.sp,
              height: 1.sp,
              color: colorE1,
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(24.sp),
                  child: Text(
                    message,
                    style: TextStyle(fontSize: 24.sp, color: color2B),
                  ),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 24.sp, vertical: 16.sp),
              child: Align(
                alignment: Alignment.centerRight,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (cancelText.isNotEmpty)
                      GestureDetector(
                        onTap: (){
                          SmartDialog.dismiss(tag: tag);
                        },
                        child: Container(
                          height: 56.sp,
                          padding: EdgeInsets.symmetric(horizontal: 24.sp),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.all(Radius.circular(8.sp)),
                              color: colorPurpleLavender
                          ),
                          child: Center(child: Text(cancelText, style: TextStyle(fontSize: 24.sp, color: colorBlueDeep), )),
                        ),
                      ),
                    SizedBox(
                      width: 24.sp,
                    ),
                    GestureDetector(
                      onTap: (){
                        SmartDialog.dismiss(tag: tag);
                        onConfirm?.call();
                      },
                      child: Container(
                        height: 56.sp,
                        padding: EdgeInsets.symmetric(horizontal: 24.sp),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(8.sp)),
                          color: colorBlueDeep
                        ),
                        child: Center(child: Text(confirmText, style: TextStyle(fontSize: 24.sp, color: Colors.white), )),
                      ),
                    )
                  ],
                ),
              ),
            )
          ],
        ),
      ),
      useSystem: false,
      tag: tag);
}

final List<ExportType> exportType = [
  ExportType(ExportType.basicInfo, Lang.patient_info_and_image),
  ExportType(ExportType.basicInfoAndPdf, Lang.patient_info_and_pdf),
];

Widget buildExportSelectItem(String title, bool selected) {
  Widget selectedView() {
    return Container(
      width: 32.sp,
      height: 32.sp,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: colorBrand, width: 2.sp),
      ),
      child: Center(
        child: CircleAvatar(
          radius: 8.sp,
          backgroundColor: colorBrand,
        ),
      ),
    );
  }

  Widget unSelectedView() {
    return Container(
      width: 32.sp,
      height: 32.sp,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: colorE1, width: 2.sp),
      ),
    );
  }

  return Container(
    decoration: BoxDecoration(
      border: Border.all(color: selected ? colorBrand : colorE1, width: 1.sp),
      borderRadius: BorderRadius.all(Radius.circular(16.sp)),
    ),
    padding: EdgeInsets.all(24.sp),
    child: Row(
      children: [
        selected ? selectedView() : unSelectedView(),
        SizedBox(
          width: 16.sp,
        ),
        Text(
          title,
          style: TextStyle(fontSize: 24.sp, color: Colors.black),
        )
      ],
    ),
  );
}

class ExportType {
  static const int basicInfo = 1;
  static const int basicInfoAndPdf = 2;

  final int type;
  final String title;

  ExportType(this.type, this.title);
}

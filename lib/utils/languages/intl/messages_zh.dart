// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a zh locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'zh';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "abnormal_teeth_count":
            MessageLookupByLibrary.simpleMessage("非正常牙齿\n%i颗"),
        "about_lyoral": MessageLookupByLibrary.simpleMessage("关于灵芽"),
        "about_plaque": MessageLookupByLibrary.simpleMessage("关于牙菌斑"),
        "account_login": MessageLookupByLibrary.simpleMessage("已有账号，去登录"),
        "account_login_other_device":
            MessageLookupByLibrary.simpleMessage("账号已在其他设备登录"),
        "account_setting": MessageLookupByLibrary.simpleMessage("账号设置"),
        "activity_list": MessageLookupByLibrary.simpleMessage("活动列表"),
        "activity_upload_status":
            MessageLookupByLibrary.simpleMessage("已上传：%d  待上传：%d"),
        "add_collect": MessageLookupByLibrary.simpleMessage("新建活动"),
        "add_diagnose": MessageLookupByLibrary.simpleMessage("新建初诊"),
        "add_mooeli_monitor": MessageLookupByLibrary.simpleMessage("添加监控协议"),
        "add_mouth_data_collect":
            MessageLookupByLibrary.simpleMessage("新建口腔数据采集"),
        "adjust_view": MessageLookupByLibrary.simpleMessage("调整画面"),
        "adjust_volume":
            MessageLookupByLibrary.simpleMessage("1.音量调整至合适大小，保持网络通畅。"),
        "advice_bigger_iscan":
            MessageLookupByLibrary.simpleMessage("建议你选择合适的开口器，方便医生查看你的牙齿情况。"),
        "age": MessageLookupByLibrary.simpleMessage("年龄"),
        "agree_and_accept": MessageLookupByLibrary.simpleMessage("同意并接受"),
        "agree_and_continue": MessageLookupByLibrary.simpleMessage("同意并继续"),
        "ai_analysis_fail": MessageLookupByLibrary.simpleMessage("AI分析失败"),
        "ai_analysis_fail_report": MessageLookupByLibrary.simpleMessage(
            "哎呀，AI分析出了点岔子，不妨点击“重新分析”，或者咨询客服小伙伴哦！当然您也可以继续做报告～"),
        "ai_analysis_overtime": MessageLookupByLibrary.simpleMessage("AI分析超时"),
        "ai_analysis_process": MessageLookupByLibrary.simpleMessage("正在进行AI分析"),
        "ai_analysis_process_report": MessageLookupByLibrary.simpleMessage(
            "预计一小时内就能见到AI分析结果啦，不妨稍后再来！当然您也可以继续做报告～"),
        "ai_analytics": MessageLookupByLibrary.simpleMessage("AI分析结果"),
        "ai_result_modified": MessageLookupByLibrary.simpleMessage("已修改AI结果"),
        "ai_status_analyzing":
            MessageLookupByLibrary.simpleMessage("正在进行AI分析......\n预计2小时内可查看结果"),
        "ai_status_fail":
            MessageLookupByLibrary.simpleMessage("抱歉AI分析失败，请点击重新分析，或联系客服"),
        "ai_status_no_result":
            MessageLookupByLibrary.simpleMessage("口内照不规范，抱歉无法进行AI分析"),
        "ai_status_overtime":
            MessageLookupByLibrary.simpleMessage("AI分析超时，请点击重新分析"),
        "all": MessageLookupByLibrary.simpleMessage("全部"),
        "all_teeth_count": MessageLookupByLibrary.simpleMessage("全部牙齿 %i 颗"),
        "already_newest_version":
            MessageLookupByLibrary.simpleMessage("当前已是最新版本"),
        "analytic_result": MessageLookupByLibrary.simpleMessage("分析结果"),
        "analytics_5001": MessageLookupByLibrary.simpleMessage("全颌曲面断层片"),
        "analytics_5002": MessageLookupByLibrary.simpleMessage("正面像分析报告"),
        "analytics_5003": MessageLookupByLibrary.simpleMessage("侧面像分析"),
        "analytics_5004": MessageLookupByLibrary.simpleMessage("口内照分析"),
        "analytics_5021": MessageLookupByLibrary.simpleMessage("头影分析"),
        "analytics_5022": MessageLookupByLibrary.simpleMessage("骨龄分析"),
        "analytics_5023": MessageLookupByLibrary.simpleMessage("气道分析"),
        "analytics_5024": MessageLookupByLibrary.simpleMessage("VTO分析"),
        "analytics_5031": MessageLookupByLibrary.simpleMessage("微笑像分析"),
        "analytics_5032": MessageLookupByLibrary.simpleMessage("微笑模拟分析"),
        "analytics_5101": MessageLookupByLibrary.simpleMessage("原始模型分析"),
        "analytics_5102": MessageLookupByLibrary.simpleMessage("分割模型分析"),
        "analytics_5103": MessageLookupByLibrary.simpleMessage("根骨模型分析"),
        "analytics_5111": MessageLookupByLibrary.simpleMessage("无约束排牙分析"),
        "analytics_5112": MessageLookupByLibrary.simpleMessage("工单排牙分析"),
        "analytics_5202":
            MessageLookupByLibrary.simpleMessage("二维多平面重建（MPR）分析"),
        "analytics_5203": MessageLookupByLibrary.simpleMessage("曲面重建（CPR）分析"),
        "and": MessageLookupByLibrary.simpleMessage("和"),
        "apply_destroy": MessageLookupByLibrary.simpleMessage("申请注销"),
        "apply_destroy_account": MessageLookupByLibrary.simpleMessage("申请注销账号"),
        "attention": MessageLookupByLibrary.simpleMessage("注意"),
        "auto_lock_screen": MessageLookupByLibrary.simpleMessage("自动锁屏"),
        "auto_lock_screen_10m": MessageLookupByLibrary.simpleMessage("10分钟"),
        "auto_lock_screen_15s": MessageLookupByLibrary.simpleMessage("15秒"),
        "auto_lock_screen_1m": MessageLookupByLibrary.simpleMessage("1分钟"),
        "auto_lock_screen_2m": MessageLookupByLibrary.simpleMessage("2分钟"),
        "auto_lock_screen_30m": MessageLookupByLibrary.simpleMessage("30分钟"),
        "auto_lock_screen_30s": MessageLookupByLibrary.simpleMessage("30秒"),
        "auto_lock_screen_5m": MessageLookupByLibrary.simpleMessage("5分钟"),
        "auto_lock_screen_never": MessageLookupByLibrary.simpleMessage("永不"),
        "auto_register_for_new":
            MessageLookupByLibrary.simpleMessage("未注册手机将自动为您注册账号"),
        "auto_saved": MessageLookupByLibrary.simpleMessage("已为您自动保存"),
        "avatar": MessageLookupByLibrary.simpleMessage("头像"),
        "baby_tooth": MessageLookupByLibrary.simpleMessage("乳牙"),
        "back": MessageLookupByLibrary.simpleMessage("返回"),
        "bad_habit": MessageLookupByLibrary.simpleMessage("不良习惯"),
        "base_clinical_check": MessageLookupByLibrary.simpleMessage("一般临床检查"),
        "base_info": MessageLookupByLibrary.simpleMessage("基本资料"),
        "bat_relation": MessageLookupByLibrary.simpleMessage("咬合关系"),
        "batch_select_all_record": MessageLookupByLibrary.simpleMessage("选择全部"),
        "batch_select_record": MessageLookupByLibrary.simpleMessage("批量操作"),
        "bind_code_error": MessageLookupByLibrary.simpleMessage("绑码异常"),
        "bind_code_success": MessageLookupByLibrary.simpleMessage("绑定成功"),
        "birthday": MessageLookupByLibrary.simpleMessage("出生日期"),
        "birthday_year": MessageLookupByLibrary.simpleMessage("出生年份"),
        "bite_back_teeth": MessageLookupByLibrary.simpleMessage("未自然咬合"),
        "bone_dl_cs1": MessageLookupByLibrary.simpleMessage(
            "一般2年后，将达到下颌骨生长发育高峰。有80%~100%生长潜力。"),
        "bone_dl_cs2": MessageLookupByLibrary.simpleMessage(
            "一般1年后，将达到下颌骨生长发育高峰。有65%~85生长潜力。"),
        "bone_dl_cs3": MessageLookupByLibrary.simpleMessage(
            "1年内，将达到下颌骨生长发育高峰。有25%~65%生长潜力。"),
        "bone_dl_cs4": MessageLookupByLibrary.simpleMessage(
            "1-2年前，已达到下颌骨生长发育高峰。有10%~25%生长潜力。"),
        "bone_dl_cs5": MessageLookupByLibrary.simpleMessage(
            "起码1年前，下颌骨生长发育高峰已结束。有5%~10%生长潜力。"),
        "bone_dl_cs6":
            MessageLookupByLibrary.simpleMessage("起码2年前，下颌骨生长发育高峰已结束。无生长潜力。"),
        "bone_learning": MessageLookupByLibrary.simpleMessage("深度学习分析法"),
        "bone_period_cs1": MessageLookupByLibrary.simpleMessage("CS1起始期"),
        "bone_period_cs2": MessageLookupByLibrary.simpleMessage("CS2快速期"),
        "bone_period_cs23":
            MessageLookupByLibrary.simpleMessage("CS2快速期/CS3过渡期"),
        "bone_period_cs3": MessageLookupByLibrary.simpleMessage("CS3过渡期"),
        "bone_period_cs34":
            MessageLookupByLibrary.simpleMessage("CS3过渡期/CS4减速期"),
        "bone_period_cs4": MessageLookupByLibrary.simpleMessage("CS4减速期"),
        "bone_period_cs5": MessageLookupByLibrary.simpleMessage("CS5成熟期"),
        "bone_period_cs6": MessageLookupByLibrary.simpleMessage("CS6完成期"),
        "bone_qualitative": MessageLookupByLibrary.simpleMessage("骨龄定性分析法"),
        "bone_quantitative": MessageLookupByLibrary.simpleMessage("骨龄定量分析法"),
        "bone_ration_1": MessageLookupByLibrary.simpleMessage("第Ⅰ期加速期"),
        "bone_ration_2": MessageLookupByLibrary.simpleMessage("第Ⅱ期高速期"),
        "bone_ration_3": MessageLookupByLibrary.simpleMessage("第Ⅲ期减速期"),
        "bone_ration_4": MessageLookupByLibrary.simpleMessage("第Ⅳ期完成期"),
        "bony": MessageLookupByLibrary.simpleMessage("骨性"),
        "buccal_corridors": MessageLookupByLibrary.simpleMessage("颊间隙"),
        "buccal_space_1":
            MessageLookupByLibrary.simpleMessage("颊间隙小，宽微笑，符合大众审美。"),
        "buccal_space_2": MessageLookupByLibrary.simpleMessage("颊间隙较小，中宽微笑。"),
        "buccal_space_3": MessageLookupByLibrary.simpleMessage("颊间隙中等，中等微笑。"),
        "buccal_space_4": MessageLookupByLibrary.simpleMessage("颊间隙较大，中窄微笑。"),
        "buccal_space_5": MessageLookupByLibrary.simpleMessage("颊间隙大，窄微笑。"),
        "buy_vip_tip":
            MessageLookupByLibrary.simpleMessage("当前账号该功能使用次数已超限，如有疑问请咨询客服"),
        "cache": MessageLookupByLibrary.simpleMessage("缓存"),
        "cancel": MessageLookupByLibrary.simpleMessage("取消"),
        "cancel_binding": MessageLookupByLibrary.simpleMessage("解除绑定"),
        "cancel_binding_confirm":
            MessageLookupByLibrary.simpleMessage("确认取消条形码绑定？"),
        "cancel_login": MessageLookupByLibrary.simpleMessage("取消登录"),
        "cancel_select_all": MessageLookupByLibrary.simpleMessage("取消全选"),
        "canine_relationship": MessageLookupByLibrary.simpleMessage("尖牙关系"),
        "cannot_destroy_account": MessageLookupByLibrary.simpleMessage("无法注销"),
        "cannot_find_activity":
            MessageLookupByLibrary.simpleMessage("没有找到“__”相关活动"),
        "cannot_find_case":
            MessageLookupByLibrary.simpleMessage("没有找到“__”相关病例"),
        "cannot_find_mouth_scan":
            MessageLookupByLibrary.simpleMessage("请重拍缺失的口内照并提交，感谢您的理解与支持"),
        "cannot_find_network":
            MessageLookupByLibrary.simpleMessage("未找到可用的互联网"),
        "cannot_find_record":
            MessageLookupByLibrary.simpleMessage("没有找到“__”相关被采集人"),
        "cannot_judge": MessageLookupByLibrary.simpleMessage("无法判断"),
        "cannot_receive_code": MessageLookupByLibrary.simpleMessage("收不到验证码"),
        "case_children": MessageLookupByLibrary.simpleMessage("儿牙病历"),
        "case_children_desc": MessageLookupByLibrary.simpleMessage("（儿科接诊专用）"),
        "case_code": MessageLookupByLibrary.simpleMessage("病例ID号"),
        "case_detail": MessageLookupByLibrary.simpleMessage("病例详情"),
        "case_doc": MessageLookupByLibrary.simpleMessage("病例档案"),
        "case_docs": MessageLookupByLibrary.simpleMessage("病例档案"),
        "case_general": MessageLookupByLibrary.simpleMessage("全科病例"),
        "case_info": MessageLookupByLibrary.simpleMessage("病例资料"),
        "case_list": MessageLookupByLibrary.simpleMessage("病例列表"),
        "case_no_contract":
            MessageLookupByLibrary.simpleMessage("暂无疗程，请前往网页端灵芽创建"),
        "case_no_doc":
            MessageLookupByLibrary.simpleMessage("暂无病例档案，请前往网页端灵芽创建"),
        "case_orthodontics": MessageLookupByLibrary.simpleMessage("正畸病例"),
        "cbct_photos": MessageLookupByLibrary.simpleMessage("CBCT"),
        "center_tooth": MessageLookupByLibrary.simpleMessage("门牙"),
        "change_language": MessageLookupByLibrary.simpleMessage("切换语言"),
        "change_mail": MessageLookupByLibrary.simpleMessage("更换绑定邮箱"),
        "change_mail_success": MessageLookupByLibrary.simpleMessage("邮箱更换成功"),
        "check_version": MessageLookupByLibrary.simpleMessage("检测更新"),
        "check_your_device_ultra": MessageLookupByLibrary.simpleMessage(
            "检测到您的设备是 Ultra Wi-Fi扫描装置，此设备无法进行牙菌斑检测哦~"),
        "check_your_device_ultra_home": MessageLookupByLibrary.simpleMessage(
            "检测到您的设备是 Ultra Wi-Fi扫描装置，此设备无法进行牙菌斑检测哦~App将退回首页并帮您选择正确的设备。"),
        "chief_complaint": MessageLookupByLibrary.simpleMessage("主诉"),
        "children_bad_habit_question":
            MessageLookupByLibrary.simpleMessage("儿童不良习惯调查问卷"),
        "choose_from_gallery": MessageLookupByLibrary.simpleMessage("从相册选取"),
        "clear": MessageLookupByLibrary.simpleMessage("清理"),
        "clear_cache": MessageLookupByLibrary.simpleMessage("清除缓存"),
        "clear_cache_desc": MessageLookupByLibrary.simpleMessage(
            "缓存是使用灵芽过程中产生的临时数据，清理缓存不会影响灵芽的正常使用。"),
        "clear_cache_finish": MessageLookupByLibrary.simpleMessage("缓存文件已清除!"),
        "clear_data": MessageLookupByLibrary.simpleMessage("清理数据"),
        "clear_data_desc1": MessageLookupByLibrary.simpleMessage(
            "清理之后，待上传、上传中和上传失败的数据彻底被清空，不可恢复。"),
        "clear_data_desc2":
            MessageLookupByLibrary.simpleMessage("请确保所有必要数据已上传成功之后再来清理。"),
        "clear_scan_data_finish":
            MessageLookupByLibrary.simpleMessage("口采数据文件已清除！"),
        "click_clear_cache": MessageLookupByLibrary.simpleMessage("点击清除"),
        "click_enter": MessageLookupByLibrary.simpleMessage("点击进入"),
        "click_research": MessageLookupByLibrary.simpleMessage("点击再次搜索"),
        "click_retry": MessageLookupByLibrary.simpleMessage("点击重试"),
        "click_select_age": MessageLookupByLibrary.simpleMessage("点击选择年龄"),
        "click_to_set": MessageLookupByLibrary.simpleMessage("点击设置"),
        "close": MessageLookupByLibrary.simpleMessage("关闭"),
        "close_ultra_tip": MessageLookupByLibrary.simpleMessage(
            "在提交扫描或退出之前，请连接至可用的网络（把手机WiFi关闭再开启，或使用手机流量），并且长按设备关机哦～"),
        "code_has_been_bound":
            MessageLookupByLibrary.simpleMessage("此码已被占用，请更换新码"),
        "code_has_been_used": MessageLookupByLibrary.simpleMessage("（条形码已被占用）"),
        "code_has_sent_mail": MessageLookupByLibrary.simpleMessage("已向%s发送验证码"),
        "code_will_send_mail":
            MessageLookupByLibrary.simpleMessage("验证码将会发送至您的注册邮箱"),
        "collapse": MessageLookupByLibrary.simpleMessage("收起"),
        "collect_data_name": MessageLookupByLibrary.simpleMessage("采集活动名称"),
        "collect_phone": MessageLookupByLibrary.simpleMessage("手机号"),
        "color": MessageLookupByLibrary.simpleMessage("颜色"),
        "come_on_later": MessageLookupByLibrary.simpleMessage("稍后再来"),
        "commit": MessageLookupByLibrary.simpleMessage("提交"),
        "complete": MessageLookupByLibrary.simpleMessage("完成"),
        "complete_record": MessageLookupByLibrary.simpleMessage("完善记录"),
        "complete_scan": MessageLookupByLibrary.simpleMessage("完成扫描"),
        "complete_treat": MessageLookupByLibrary.simpleMessage("已完成"),
        "complete_user_profile": MessageLookupByLibrary.simpleMessage("完善个人信息"),
        "conclusion": MessageLookupByLibrary.simpleMessage("结论"),
        "conencting_camera":
            MessageLookupByLibrary.simpleMessage("正在连接相机，请稍等..."),
        "confirm": MessageLookupByLibrary.simpleMessage("确定"),
        "confirm_delete_activity":
            MessageLookupByLibrary.simpleMessage("确认删除本次口腔数据采集吗？"),
        "confirm_delete_activity_name":
            MessageLookupByLibrary.simpleMessage("删除活动"),
        "confirm_delete_smile_photo":
            MessageLookupByLibrary.simpleMessage("确认要删除照片吗？"),
        "confirm_destroy_account": MessageLookupByLibrary.simpleMessage("确认注销"),
        "confirm_logout": MessageLookupByLibrary.simpleMessage("确认退出登录吗？"),
        "confirm_logout_offline": MessageLookupByLibrary.simpleMessage(
            "当前为“离线状态”，当前账号数据已自动导出至本地，退出后请在联网状态下登录其他账号，否则将无法继续使用乐齿拍系统"),
        "confirm_modify": MessageLookupByLibrary.simpleMessage("确认修改"),
        "confirm_modify_code": MessageLookupByLibrary.simpleMessage("确定修改"),
        "confirm_ok": MessageLookupByLibrary.simpleMessage("确定"),
        "confirm_password": MessageLookupByLibrary.simpleMessage("确认密码"),
        "connect": MessageLookupByLibrary.simpleMessage("连接"),
        "connect_device_fail": MessageLookupByLibrary.simpleMessage("连不上设备？"),
        "connect_interrupted": MessageLookupByLibrary.simpleMessage("连接中断"),
        "connect_retry": MessageLookupByLibrary.simpleMessage("请重试连接，或尝试："),
        "connect_ultra_tip_content11":
            MessageLookupByLibrary.simpleMessage("长按关闭设备，"),
        "connect_ultra_tip_content12":
            MessageLookupByLibrary.simpleMessage("再次长按开启并连接"),
        "connect_ultra_tip_content13":
            MessageLookupByLibrary.simpleMessage("彻底关闭灵芽应用程序，再次打开"),
        "connect_ultra_tip_content14":
            MessageLookupByLibrary.simpleMessage("务必确保连接的是灵芽系列产品"),
        "connect_ultra_tip_content21":
            MessageLookupByLibrary.simpleMessage("给设备充电后，"),
        "connect_ultra_tip_content22":
            MessageLookupByLibrary.simpleMessage("再次长按开启并连接"),
        "connect_ultra_tip_title1":
            MessageLookupByLibrary.simpleMessage("请尝试："),
        "connecting": MessageLookupByLibrary.simpleMessage("连接中"),
        "connecting_dot": MessageLookupByLibrary.simpleMessage("连接中......"),
        "connecting_network": MessageLookupByLibrary.simpleMessage("正在连接互联网……"),
        "contact_way": MessageLookupByLibrary.simpleMessage(
            "若您在使用APP时遇到任何问题或有更好的建议，请扫描客服微信二维码，我们将一对一进行解答："),
        "continue_login": MessageLookupByLibrary.simpleMessage("继续登录"),
        "continue_make_report": MessageLookupByLibrary.simpleMessage("继续做报告"),
        "continue_step": MessageLookupByLibrary.simpleMessage("继续"),
        "contract_info": MessageLookupByLibrary.simpleMessage("资料库"),
        "convert_project": MessageLookupByLibrary.simpleMessage("潜在治疗方向"),
        "convert_project_tip": MessageLookupByLibrary.simpleMessage(
            "该标签内容基于被采集人的AI分析结果自动生成推荐,旨在指出患者潜在治疗方向。\n仅供参考,最终治疗方案需由专业医生结合其实际情况综合判定。"),
        "copy": MessageLookupByLibrary.simpleMessage("复制"),
        "copy_success": MessageLookupByLibrary.simpleMessage("复制成功"),
        "cranial": MessageLookupByLibrary.simpleMessage("头颅侧位片"),
        "create": MessageLookupByLibrary.simpleMessage("创建"),
        "create_activity": MessageLookupByLibrary.simpleMessage("新建活动"),
        "create_case": MessageLookupByLibrary.simpleMessage("创建"),
        "create_new_scan": MessageLookupByLibrary.simpleMessage("新建患者扫描"),
        "create_patient": MessageLookupByLibrary.simpleMessage("新建患者"),
        "create_success": MessageLookupByLibrary.simpleMessage("创建成功"),
        "create_time": MessageLookupByLibrary.simpleMessage("创建时间"),
        "crowding": MessageLookupByLibrary.simpleMessage("拥挤度"),
        "current_account": MessageLookupByLibrary.simpleMessage("当前账号:"),
        "current_audio": MessageLookupByLibrary.simpleMessage("当前音量:"),
        "current_brightness": MessageLookupByLibrary.simpleMessage("当前亮度:"),
        "current_cache": MessageLookupByLibrary.simpleMessage("当前缓存: "),
        "data": MessageLookupByLibrary.simpleMessage("数据"),
        "data_in_cover":
            MessageLookupByLibrary.simpleMessage("以下信息会自动生成在口腔报告的封面"),
        "data_panel": MessageLookupByLibrary.simpleMessage("数据看板"),
        "data_storage": MessageLookupByLibrary.simpleMessage("存储空间"),
        "date": MessageLookupByLibrary.simpleMessage("%1s 年 %2s 月 %3s 日"),
        "deal_project": MessageLookupByLibrary.simpleMessage("意向项目"),
        "declare_case": MessageLookupByLibrary.simpleMessage(
            "由灵芽智能口腔医疗平台生成，此报告仅供参考，不做最后诊断依据"),
        "default_type": MessageLookupByLibrary.simpleMessage("默认"),
        "delete": MessageLookupByLibrary.simpleMessage("删除"),
        "delete_activity": MessageLookupByLibrary.simpleMessage("删除活动"),
        "delete_activity_not_recoverd":
            MessageLookupByLibrary.simpleMessage("删除所选活动后，数据将无法找回"),
        "delete_diagnose_content": MessageLookupByLibrary.simpleMessage(
            "确认删除后，该初诊患者将在网页端同步移除，且数据一旦删除不可恢复"),
        "delete_diagnose_title":
            MessageLookupByLibrary.simpleMessage("您确定要删除该初诊患者吗"),
        "delete_not_recoverd":
            MessageLookupByLibrary.simpleMessage("删除后，数据将无法找回"),
        "delete_plaque": MessageLookupByLibrary.simpleMessage("确定删除本次牙菌斑检测吗？"),
        "delete_record": MessageLookupByLibrary.simpleMessage("删除记录"),
        "delete_success": MessageLookupByLibrary.simpleMessage("删除成功"),
        "delete_user_record": MessageLookupByLibrary.simpleMessage("删除患者数据"),
        "delete_user_record_confirm":
            MessageLookupByLibrary.simpleMessage("确认删除这些患者数据吗？"),
        "dental_caries": MessageLookupByLibrary.simpleMessage("龋齿"),
        "destroy_account": MessageLookupByLibrary.simpleMessage("注销账号"),
        "destroy_account_alert":
            MessageLookupByLibrary.simpleMessage("注销账号后不可恢复，请谨慎操作！"),
        "destroy_account_means": MessageLookupByLibrary.simpleMessage("注销代表"),
        "destroy_account_success":
            MessageLookupByLibrary.simpleMessage("账号已成功注销"),
        "destroy_account_tip1": MessageLookupByLibrary.simpleMessage(
            "• 所有您拥有的数据（包括病例信息、个人信息、临时文件）将被完全删除且不可恢复\n\n• 若您开通过%s监控，则所有疗程将被自动结束。所有的监控数据将被完全删除且不可恢复，但患者端仍可以查看历史提交与历史回复\n\n• 所有相关账号信息将被完全删除且不可恢复\n\n• 您在灵芽系统内的任何未到期的会员或资格、任何进行中的订单会自动于注销日结束\n\n• 注销后，您可以使用您的信息注册全新的灵芽账号"),
        "detect_history": MessageLookupByLibrary.simpleMessage("检测记录"),
        "detect_to_record": MessageLookupByLibrary.simpleMessage("检测录入"),
        "detected_usb": MessageLookupByLibrary.simpleMessage("检测到U盘"),
        "development_status": MessageLookupByLibrary.simpleMessage("发育状况"),
        "device_battery": MessageLookupByLibrary.simpleMessage("设备电量"),
        "device_dead_answer":
            MessageLookupByLibrary.simpleMessage("请插入电源线再拔下，即可恢复"),
        "device_dead_question":
            MessageLookupByLibrary.simpleMessage("设备无法关机怎么办？"),
        "diagnose_detail": MessageLookupByLibrary.simpleMessage("初诊详情"),
        "diagnose_info": MessageLookupByLibrary.simpleMessage("病例资料"),
        "diagnose_report": MessageLookupByLibrary.simpleMessage("初诊报告"),
        "diagnose_report_tip":
            MessageLookupByLibrary.simpleMessage("以下内容填写后将展示在报告中"),
        "diagnosis": MessageLookupByLibrary.simpleMessage("诊断"),
        "direct_to_next": MessageLookupByLibrary.simpleMessage("直接进入下一步"),
        "disease_0": MessageLookupByLibrary.simpleMessage("残根"),
        "disease_1": MessageLookupByLibrary.simpleMessage("残冠"),
        "disease_10": MessageLookupByLibrary.simpleMessage("牙齿扭转"),
        "disease_11": MessageLookupByLibrary.simpleMessage("牙颜色异常（牙髓病变）"),
        "disease_12": MessageLookupByLibrary.simpleMessage("牙列不齐"),
        "disease_13": MessageLookupByLibrary.simpleMessage("牙列间隙"),
        "disease_14": MessageLookupByLibrary.simpleMessage("全冠"),
        "disease_2": MessageLookupByLibrary.simpleMessage("龋坏"),
        "disease_3": MessageLookupByLibrary.simpleMessage("脱矿"),
        "disease_4": MessageLookupByLibrary.simpleMessage("色素沉着"),
        "disease_5": MessageLookupByLibrary.simpleMessage("楔状缺损"),
        "disease_6": MessageLookupByLibrary.simpleMessage("软垢"),
        "disease_7": MessageLookupByLibrary.simpleMessage("牙周炎"),
        "disease_8": MessageLookupByLibrary.simpleMessage("牙结石"),
        "disease_9": MessageLookupByLibrary.simpleMessage("磨损"),
        "disease_classification": MessageLookupByLibrary.simpleMessage("疾病分类"),
        "disease_nature_time": MessageLookupByLibrary.simpleMessage("疾病性质与时间"),
        "doc_base_info": MessageLookupByLibrary.simpleMessage("基本信息"),
        "doc_clinical_info": MessageLookupByLibrary.simpleMessage("临床信息"),
        "doc_diagnose_info": MessageLookupByLibrary.simpleMessage("诊断结果"),
        "doc_inspect_info": MessageLookupByLibrary.simpleMessage("检查报告"),
        "doc_treatment_info": MessageLookupByLibrary.simpleMessage("治疗方案及其他"),
        "doctor": MessageLookupByLibrary.simpleMessage("医生"),
        "double_click_continue": MessageLookupByLibrary.simpleMessage("点击继续"),
        "double_click_scan": MessageLookupByLibrary.simpleMessage("点击开始扫描"),
        "download_complete": MessageLookupByLibrary.simpleMessage("下载完成"),
        "downloading": MessageLookupByLibrary.simpleMessage("正在下载..."),
        "edit": MessageLookupByLibrary.simpleMessage("编辑"),
        "edit_activity": MessageLookupByLibrary.simpleMessage("编辑活动"),
        "edit_mouth_data_collect":
            MessageLookupByLibrary.simpleMessage("编辑口腔数据采集"),
        "edit_scan_record": MessageLookupByLibrary.simpleMessage("编辑患者扫描"),
        "empty_nickname": MessageLookupByLibrary.simpleMessage("昵称不能为空"),
        "end_time": MessageLookupByLibrary.simpleMessage("结束时间"),
        "ensure_indicator_light":
            MessageLookupByLibrary.simpleMessage("确保指示灯已亮，将设备靠近手机"),
        "entry_panel": MessageLookupByLibrary.simpleMessage("功能入口"),
        "example": MessageLookupByLibrary.simpleMessage("示范"),
        "exit": MessageLookupByLibrary.simpleMessage("退出"),
        "exit_app": MessageLookupByLibrary.simpleMessage("退出灵芽"),
        "exit_batch_select": MessageLookupByLibrary.simpleMessage("退出批量操作"),
        "expand": MessageLookupByLibrary.simpleMessage("展开"),
        "export": MessageLookupByLibrary.simpleMessage("导出"),
        "export_fail": MessageLookupByLibrary.simpleMessage("导出失败"),
        "export_ing": MessageLookupByLibrary.simpleMessage("导出中..."),
        "export_processing": MessageLookupByLibrary.simpleMessage("导出中"),
        "export_retry": MessageLookupByLibrary.simpleMessage("重新导出"),
        "export_scan_data_desc": MessageLookupByLibrary.simpleMessage(
            "如果您有2024年3月1日~10月23日采集的患者数据，且还没有进行上传，请进行导出。如果不导出则数据会彻底丢失。"),
        "export_scan_data_file":
            MessageLookupByLibrary.simpleMessage("口采数据导出备份"),
        "export_success": MessageLookupByLibrary.simpleMessage("已导出"),
        "export_to_usb": MessageLookupByLibrary.simpleMessage("导出到U盘"),
        "export_to_usb_tip": MessageLookupByLibrary.simpleMessage("导出到U盘："),
        "export_u_card": MessageLookupByLibrary.simpleMessage("导出至U盘"),
        "extract_teeth": MessageLookupByLibrary.simpleMessage("智齿"),
        "face_check": MessageLookupByLibrary.simpleMessage("颜面检查"),
        "face_in_rect_smile":
            MessageLookupByLibrary.simpleMessage("将头部置于白框内，露齿微笑"),
        "face_mouth_photos": MessageLookupByLibrary.simpleMessage("面像与口内照片"),
        "face_photo": MessageLookupByLibrary.simpleMessage("面像照"),
        "face_type": MessageLookupByLibrary.simpleMessage("面型"),
        "fail_determine_canine":
            MessageLookupByLibrary.simpleMessage("至少有一颗尖牙不可见，无法判断尖牙关系"),
        "fail_determine_midline":
            MessageLookupByLibrary.simpleMessage("至少有一颗1号牙不可见，无法判断中线关系"),
        "fail_determine_molar":
            MessageLookupByLibrary.simpleMessage("至少有一颗磨牙不可见，无法判断磨牙关系"),
        "fail_reason": MessageLookupByLibrary.simpleMessage("失败原因"),
        "family_history": MessageLookupByLibrary.simpleMessage("家族病史"),
        "favor": MessageLookupByLibrary.simpleMessage("收藏"),
        "features": MessageLookupByLibrary.simpleMessage("特征"),
        "file_effect_time": MessageLookupByLibrary.simpleMessage("文件有效期: %d小时"),
        "fill_in": MessageLookupByLibrary.simpleMessage("填充"),
        "filter": MessageLookupByLibrary.simpleMessage("筛选"),
        "filter_title": MessageLookupByLibrary.simpleMessage("筛选和排序"),
        "find_newer_version": MessageLookupByLibrary.simpleMessage("发现新版本"),
        "finish_share": MessageLookupByLibrary.simpleMessage("结束共享"),
        "first_diagnose": MessageLookupByLibrary.simpleMessage("初诊"),
        "first_diagnose_Date": MessageLookupByLibrary.simpleMessage("初诊日期"),
        "first_treat": MessageLookupByLibrary.simpleMessage("初诊"),
        "follow": MessageLookupByLibrary.simpleMessage("关注"),
        "forget_password": MessageLookupByLibrary.simpleMessage("忘记密码"),
        "frequent_questions": MessageLookupByLibrary.simpleMessage("常见问题"),
        "friendly_tip": MessageLookupByLibrary.simpleMessage("温馨提示"),
        "front": MessageLookupByLibrary.simpleMessage("正面"),
        "front_negative_overjet": MessageLookupByLibrary.simpleMessage("前牙反𬌗"),
        "gender": MessageLookupByLibrary.simpleMessage("性别"),
        "gender_man": MessageLookupByLibrary.simpleMessage("男"),
        "gender_woman": MessageLookupByLibrary.simpleMessage("女"),
        "general_setting": MessageLookupByLibrary.simpleMessage("通用设置"),
        "generate_report": MessageLookupByLibrary.simpleMessage("生成报告"),
        "generate_time": MessageLookupByLibrary.simpleMessage("生成时间"),
        "gently_open_maxillary":
            MessageLookupByLibrary.simpleMessage("牙齿张开幅度如图所示"),
        "gently_stretch_teeth":
            MessageLookupByLibrary.simpleMessage("牙齿张开幅度过小"),
        "gently_stretch_teeth_not_wild":
            MessageLookupByLibrary.simpleMessage("牙齿张开幅度过大"),
        "get_report_in_web":
            MessageLookupByLibrary.simpleMessage("如需制作报告，请使用网页版灵芽"),
        "get_verify_code": MessageLookupByLibrary.simpleMessage("获取验证码"),
        "give_up": MessageLookupByLibrary.simpleMessage("放弃"),
        "giveup_connect": MessageLookupByLibrary.simpleMessage("放弃连接"),
        "giveup_scan": MessageLookupByLibrary.simpleMessage("放弃搜索"),
        "go_app_store": MessageLookupByLibrary.simpleMessage("前往应用商店"),
        "go_setting": MessageLookupByLibrary.simpleMessage("去设置"),
        "go_setting_network":
            MessageLookupByLibrary.simpleMessage("请前往系统的WIFI设置，或打开数据流量"),
        "go_to_complete": MessageLookupByLibrary.simpleMessage("去完善"),
        "go_upgrade": MessageLookupByLibrary.simpleMessage("去更新"),
        "go_upload": MessageLookupByLibrary.simpleMessage("去上传"),
        "go_view": MessageLookupByLibrary.simpleMessage("前往查看"),
        "go_yingyongbao": MessageLookupByLibrary.simpleMessage("前往应用宝"),
        "good_afternoon": MessageLookupByLibrary.simpleMessage("下午好，"),
        "good_evening": MessageLookupByLibrary.simpleMessage("晚上好，"),
        "good_morning": MessageLookupByLibrary.simpleMessage("早上好，"),
        "good_night": MessageLookupByLibrary.simpleMessage("晚上好，"),
        "good_noon": MessageLookupByLibrary.simpleMessage("中午好，"),
        "growth_type": MessageLookupByLibrary.simpleMessage("生长型"),
        "guide": MessageLookupByLibrary.simpleMessage("常见问题"),
        "have_question": MessageLookupByLibrary.simpleMessage("遇到问题？"),
        "height": MessageLookupByLibrary.simpleMessage("身高"),
        "history_allergy": MessageLookupByLibrary.simpleMessage("过敏史"),
        "history_family": MessageLookupByLibrary.simpleMessage("家族史"),
        "history_treatment": MessageLookupByLibrary.simpleMessage("治疗史"),
        "home_page": MessageLookupByLibrary.simpleMessage("工作台"),
        "hospital_diagnose": MessageLookupByLibrary.simpleMessage("院内初诊"),
        "how_observe_plaque": MessageLookupByLibrary.simpleMessage("怎么看牙菌斑"),
        "how_to_observe_plaque":
            MessageLookupByLibrary.simpleMessage("如何观察牙菌斑？"),
        "i_knew": MessageLookupByLibrary.simpleMessage("我知道了"),
        "icp_code_copied": MessageLookupByLibrary.simpleMessage("备案号已复制"),
        "icp_code_only_cn": MessageLookupByLibrary.simpleMessage(
            "浙公网安备 33010602012209号      浙ICP备2021003396号-3"),
        "icp_code_system": MessageLookupByLibrary.simpleMessage("备案管理系统"),
        "import_scan": MessageLookupByLibrary.simpleMessage("导入口采患者"),
        "in_analytic": MessageLookupByLibrary.simpleMessage("分析中..."),
        "in_bottom": MessageLookupByLibrary.simpleMessage("到底了"),
        "in_treating": MessageLookupByLibrary.simpleMessage("治疗中"),
        "incorrect_mail": MessageLookupByLibrary.simpleMessage("请输入正确邮箱号"),
        "incorrect_password": MessageLookupByLibrary.simpleMessage("密码错误"),
        "incorrect_phone": MessageLookupByLibrary.simpleMessage("请输入正确手机号"),
        "incorrect_verify_code": MessageLookupByLibrary.simpleMessage("验证码错误"),
        "input_account": MessageLookupByLibrary.simpleMessage("手机号/用户名"),
        "input_activity_name": MessageLookupByLibrary.simpleMessage("输入活动名称"),
        "input_activity_name_error":
            MessageLookupByLibrary.simpleMessage("未输入名称"),
        "input_activity_search":
            MessageLookupByLibrary.simpleMessage("请输入采集活动名称/归属人"),
        "input_appeal":
            MessageLookupByLibrary.simpleMessage("请输入主要症状、部位、时间，最多500字"),
        "input_collect_name": MessageLookupByLibrary.simpleMessage("输入名称"),
        "input_mail": MessageLookupByLibrary.simpleMessage("请输入邮箱"),
        "input_name": MessageLookupByLibrary.simpleMessage("输入姓名"),
        "input_name_or_id":
            MessageLookupByLibrary.simpleMessage("请输入患者姓名或病例ID号"),
        "input_new_mail": MessageLookupByLibrary.simpleMessage("请输入新邮箱"),
        "input_new_password": MessageLookupByLibrary.simpleMessage("请输入新密码"),
        "input_nickname": MessageLookupByLibrary.simpleMessage("请输入昵称"),
        "input_old_mail": MessageLookupByLibrary.simpleMessage("请输入原邮箱"),
        "input_old_password": MessageLookupByLibrary.simpleMessage("请输入原密码"),
        "input_old_password0": MessageLookupByLibrary.simpleMessage("输入原密码"),
        "input_password": MessageLookupByLibrary.simpleMessage("密码"),
        "input_password_error": MessageLookupByLibrary.simpleMessage("密码输入错误"),
        "input_person_name": MessageLookupByLibrary.simpleMessage("输入归属人名称"),
        "input_phone": MessageLookupByLibrary.simpleMessage("请输入手机号"),
        "input_record_name":
            MessageLookupByLibrary.simpleMessage("输入完整的被采集人名称或手机号"),
        "input_record_search":
            MessageLookupByLibrary.simpleMessage("请输入患者名称/手机号"),
        "input_remark": MessageLookupByLibrary.simpleMessage("请输入文字内容，最多100字"),
        "input_tenant_worker_address":
            MessageLookupByLibrary.simpleMessage("请输入机构门诊地址，最多50个字"),
        "input_tenant_worker_name":
            MessageLookupByLibrary.simpleMessage("请输入姓名"),
        "input_tenant_worker_phone":
            MessageLookupByLibrary.simpleMessage("请输入电话号码"),
        "input_tenant_worker_qrcode":
            MessageLookupByLibrary.simpleMessage("请输入二维码说明，最多9个字"),
        "input_verify_code": MessageLookupByLibrary.simpleMessage("验证码"),
        "internal_error_retry":
            MessageLookupByLibrary.simpleMessage("服务器有点忙，我们马上让它回来！请稍后再试"),
        "internet_not_connected":
            MessageLookupByLibrary.simpleMessage("请先连接局域网，再使用此功能。"),
        "intraoral_photo": MessageLookupByLibrary.simpleMessage("口内照"),
        "invalid_nickname": MessageLookupByLibrary.simpleMessage("昵称不合规，请重新填写"),
        "joint_check": MessageLookupByLibrary.simpleMessage("关节检查"),
        "l_size": MessageLookupByLibrary.simpleMessage("L号"),
        "lan_sharing": MessageLookupByLibrary.simpleMessage("局域网共享"),
        "language_auto": MessageLookupByLibrary.simpleMessage("自动"),
        "language_setting": MessageLookupByLibrary.simpleMessage("语言设置"),
        "last_scan": MessageLookupByLibrary.simpleMessage("最近扫描"),
        "lateral_abo": MessageLookupByLibrary.simpleMessage("ABO分析法"),
        "lateral_beijing": MessageLookupByLibrary.simpleMessage("北京大学分析法"),
        "lateral_burstone": MessageLookupByLibrary.simpleMessage("Burstone分析法"),
        "lateral_downs": MessageLookupByLibrary.simpleMessage("Downs分析法"),
        "lateral_holdway": MessageLookupByLibrary.simpleMessage("Holdaway分析法"),
        "lateral_huaxi": MessageLookupByLibrary.simpleMessage("华西综合分析法"),
        "lateral_jarabak": MessageLookupByLibrary.simpleMessage("Jarabak分析法"),
        "lateral_jiuyuan": MessageLookupByLibrary.simpleMessage("九院分析法"),
        "lateral_mcnamara": MessageLookupByLibrary.simpleMessage("McNamara分析法"),
        "lateral_ricketts": MessageLookupByLibrary.simpleMessage("Ricketts分析法"),
        "lateral_riedel": MessageLookupByLibrary.simpleMessage("Riedel分析法"),
        "lateral_steiner": MessageLookupByLibrary.simpleMessage("Steiner分析法"),
        "lateral_tweed": MessageLookupByLibrary.simpleMessage("Tweed分析法"),
        "lateral_wylie": MessageLookupByLibrary.simpleMessage("Wylie分析法"),
        "left_side": MessageLookupByLibrary.simpleMessage("左侧"),
        "left_tooth": MessageLookupByLibrary.simpleMessage("左牙"),
        "light": MessageLookupByLibrary.simpleMessage("Light"),
        "light_intro": MessageLookupByLibrary.simpleMessage("Wi-Fi牙菌斑扫描"),
        "loading": MessageLookupByLibrary.simpleMessage("正在努力加载中..."),
        "loading_image_error": MessageLookupByLibrary.simpleMessage("图片加载失败"),
        "loading_images":
            MessageLookupByLibrary.simpleMessage("拼命加载中\n预计30分钟内可查看图片"),
        "loading_video": MessageLookupByLibrary.simpleMessage("正在加载视频..."),
        "local_network_finish_share_confirm":
            MessageLookupByLibrary.simpleMessage(
                "请确认您的网页端浏览器中所有数据已下载完成，再结束共享。"),
        "local_network_share_content_tip":
            MessageLookupByLibrary.simpleMessage("请选择本次共享至局域网的内容："),
        "local_network_share_direction":
            MessageLookupByLibrary.simpleMessage("当前局域网已开启，请按照以下步骤，共享数据。"),
        "local_network_share_finished":
            MessageLookupByLibrary.simpleMessage("共享已结束"),
        "local_network_share_tip":
            MessageLookupByLibrary.simpleMessage("数据下载完成之前请不要结束共享，避免造成数据丢失。"),
        "local_network_tip1":
            MessageLookupByLibrary.simpleMessage("请将您的接收设备连接至同一网络"),
        "local_network_tip2":
            MessageLookupByLibrary.simpleMessage("在任意浏览器地址栏中输入：“%s”"),
        "local_network_tip2_1": MessageLookupByLibrary.simpleMessage(
            "如果您的接收设备是windows10以下系统，请在任意浏览器地址栏中输入：“%s”"),
        "local_network_tip3":
            MessageLookupByLibrary.simpleMessage("点击下载后，病例文件会下载到接收设备中"),
        "login": MessageLookupByLibrary.simpleMessage("登录"),
        "login_asian": MessageLookupByLibrary.simpleMessage("亚太地区"),
        "login_by_code": MessageLookupByLibrary.simpleMessage("验证码登录"),
        "login_by_password": MessageLookupByLibrary.simpleMessage("密码登录"),
        "login_china": MessageLookupByLibrary.simpleMessage("中国大陆"),
        "login_desc":
            MessageLookupByLibrary.simpleMessage("AI全方位智能识别与评估，为口腔健\n康保驾护航"),
        "login_exit_desc": MessageLookupByLibrary.simpleMessage(
            "由于您长时间未进行操作或检测到账号已在其他设备登录，为了保护您的账户安全，系统已自动退出登录状态"),
        "login_mail": MessageLookupByLibrary.simpleMessage("登录邮箱"),
        "login_now": MessageLookupByLibrary.simpleMessage("立即登录"),
        "login_only_for_tenant": MessageLookupByLibrary.simpleMessage(
            "口腔数据采集功能仅对机构版账户开放，请扫描下方二维码访问乐齿拍网站申请开通!"),
        "login_push_other_device":
            MessageLookupByLibrary.simpleMessage("继续登录将导致账号在其他设备退出登录"),
        "login_show_tenant": MessageLookupByLibrary.simpleMessage("登录后显示机构"),
        "login_slogan": MessageLookupByLibrary.simpleMessage("精准洞察，守护健康"),
        "login_v3_desc": MessageLookupByLibrary.simpleMessage(
            "尊敬的用户，为了给您提供更优质的服务，我们已于10月24日凌晨更新版本，请重新登录以继续使用。感谢您的理解与支持！"),
        "logo_setting": MessageLookupByLibrary.simpleMessage("Logo设置："),
        "logout": MessageLookupByLibrary.simpleMessage("退出登录"),
        "logout_account": MessageLookupByLibrary.simpleMessage("退出账号"),
        "long_press_save_qrcode":
            MessageLookupByLibrary.simpleMessage("（长按保存二维码，微信扫码添加）"),
        "loss_teeth_count": MessageLookupByLibrary.simpleMessage("牙齿缺失\n%i颗"),
        "lost_all_teeth": MessageLookupByLibrary.simpleMessage("疑似无牙颌"),
        "lost_lower_teeth": MessageLookupByLibrary.simpleMessage("疑似下颌牙列缺失"),
        "lost_teeth": MessageLookupByLibrary.simpleMessage("牙号缺失"),
        "lost_upper_teeth": MessageLookupByLibrary.simpleMessage("疑似上颌牙列缺失"),
        "low_battery_alert":
            MessageLookupByLibrary.simpleMessage("设备的电量过低，可能出现卡顿或断连，请尽快充电哦！"),
        "lyoral_page": MessageLookupByLibrary.simpleMessage("病例"),
        "lyoral_web": MessageLookupByLibrary.simpleMessage("网页端灵芽"),
        "m_size": MessageLookupByLibrary.simpleMessage("M号"),
        "mail": MessageLookupByLibrary.simpleMessage("邮箱"),
        "mail_already_register": MessageLookupByLibrary.simpleMessage("邮箱已注册"),
        "mail_not_register": MessageLookupByLibrary.simpleMessage("邮箱未注册"),
        "main_require": MessageLookupByLibrary.simpleMessage("主诉"),
        "make_mouth_lighten": MessageLookupByLibrary.simpleMessage("画面不够亮"),
        "make_mouth_lighten_action":
            MessageLookupByLibrary.simpleMessage("调整手机或开口器位置，使闪光灯置于开口器取景框内"),
        "make_report": MessageLookupByLibrary.simpleMessage("制作报告"),
        "make_ultra_power_on": MessageLookupByLibrary.simpleMessage("长按电源键开机，"),
        "mandibular": MessageLookupByLibrary.simpleMessage("下颌"),
        "maxillary": MessageLookupByLibrary.simpleMessage("上颌"),
        "measure_desc": MessageLookupByLibrary.simpleMessage("说明"),
        "measure_name": MessageLookupByLibrary.simpleMessage("测量项目"),
        "measure_norm": MessageLookupByLibrary.simpleMessage("标准值"),
        "measure_value": MessageLookupByLibrary.simpleMessage("测量值"),
        "medical_start_time": MessageLookupByLibrary.simpleMessage("正畸开始时间"),
        "medication_history": MessageLookupByLibrary.simpleMessage("用药史"),
        "message_page": MessageLookupByLibrary.simpleMessage("消息"),
        "midline_analysis": MessageLookupByLibrary.simpleMessage("中线分析"),
        "midline_deviation": MessageLookupByLibrary.simpleMessage("中线偏斜"),
        "midline_left": MessageLookupByLibrary.simpleMessage("下颌相对上颌中线偏左"),
        "midline_normal": MessageLookupByLibrary.simpleMessage("下颌相对上颌中线对齐"),
        "midline_relation": MessageLookupByLibrary.simpleMessage("中线关系"),
        "midline_relationship": MessageLookupByLibrary.simpleMessage("中线关系"),
        "midline_right": MessageLookupByLibrary.simpleMessage("下颌相对上颌中线偏右"),
        "mobile": MessageLookupByLibrary.simpleMessage("电话"),
        "mobile_wlan": MessageLookupByLibrary.simpleMessage("移动网络"),
        "modify_password": MessageLookupByLibrary.simpleMessage("修改密码"),
        "modify_password_success":
            MessageLookupByLibrary.simpleMessage("修改密码成功"),
        "molar_class": MessageLookupByLibrary.simpleMessage(" %s 类"),
        "molar_relation": MessageLookupByLibrary.simpleMessage("磨牙咬合关系"),
        "molar_relationship": MessageLookupByLibrary.simpleMessage("磨牙关系"),
        "monitor_name_clear": MessageLookupByLibrary.simpleMessage("隐形矫正监控"),
        "monitor_name_fixed": MessageLookupByLibrary.simpleMessage("固定矫正监控"),
        "monitor_name_general": MessageLookupByLibrary.simpleMessage("口腔全科观察"),
        "monitor_process": MessageLookupByLibrary.simpleMessage("监控进度"),
        "monitor_status": MessageLookupByLibrary.simpleMessage("监控状态"),
        "mouth_data_collect": MessageLookupByLibrary.simpleMessage("口腔数据采集"),
        "mouth_opening": MessageLookupByLibrary.simpleMessage("张口度"),
        "mouth_type": MessageLookupByLibrary.simpleMessage("张口型"),
        "move_mouth_prop_follow_arrow":
            MessageLookupByLibrary.simpleMessage("方向错误，请跟随箭头方向移动开口器"),
        "multiple_language": MessageLookupByLibrary.simpleMessage("多语言"),
        "multiple_select": MessageLookupByLibrary.simpleMessage("多选"),
        "my_center": MessageLookupByLibrary.simpleMessage("设置中心"),
        "my_file": MessageLookupByLibrary.simpleMessage("我的文件"),
        "my_file_tip": MessageLookupByLibrary.simpleMessage(
            "为节省文件存储空间，临时文件保存时限为48小时，48小时后系统将默认删除，请及时下载文件"),
        "my_lyoral_case": MessageLookupByLibrary.simpleMessage("我的病例"),
        "my_mooeli_case": MessageLookupByLibrary.simpleMessage("MOOELI"),
        "my_page": MessageLookupByLibrary.simpleMessage("我的"),
        "name": MessageLookupByLibrary.simpleMessage("姓名"),
        "natural_bite": MessageLookupByLibrary.simpleMessage("咽口水，自然咬合"),
        "need_clear_smile_photo":
            MessageLookupByLibrary.simpleMessage("微笑模拟需保证上传高清正面微笑像"),
        "need_network": MessageLookupByLibrary.simpleMessage("网络不太顺畅，请连接可用的网络"),
        "negative_overjet": MessageLookupByLibrary.simpleMessage("反𬌗"),
        "network_error": MessageLookupByLibrary.simpleMessage("网络错误"),
        "network_risk_answer":
            MessageLookupByLibrary.simpleMessage("请选择“信任此网络”"),
        "network_risk_question":
            MessageLookupByLibrary.simpleMessage("遇到风险提醒弹窗怎么办？"),
        "network_warn_answer":
            MessageLookupByLibrary.simpleMessage("请选择“继续使用无线网络”"),
        "network_warn_question":
            MessageLookupByLibrary.simpleMessage("遇到系统的网络提醒弹窗怎么办？"),
        "network_weak":
            MessageLookupByLibrary.simpleMessage("当前网络不太顺畅，请检查网络设置"),
        "new_password": MessageLookupByLibrary.simpleMessage("新密码"),
        "new_patient_count": MessageLookupByLibrary.simpleMessage("新增患者数"),
        "new_scan": MessageLookupByLibrary.simpleMessage("新建扫描"),
        "new_user_register": MessageLookupByLibrary.simpleMessage("新用户注册"),
        "new_version": MessageLookupByLibrary.simpleMessage("新版本"),
        "next": MessageLookupByLibrary.simpleMessage("下一步"),
        "next_step": MessageLookupByLibrary.simpleMessage("下一步"),
        "nickname": MessageLookupByLibrary.simpleMessage("昵称"),
        "nickname_rule":
            MessageLookupByLibrary.simpleMessage("只能使用数字、字母、汉字和下划线"),
        "no_analytic_result":
            MessageLookupByLibrary.simpleMessage("暂无AI分析结果\n请使用灵芽网页端开启分析"),
        "no_case_login": MessageLookupByLibrary.simpleMessage("暂无病例\n请登录"),
        "no_data": MessageLookupByLibrary.simpleMessage("未加载到数据，请尝试下拉刷新"),
        "no_filter_case":
            MessageLookupByLibrary.simpleMessage("暂无符合条件的病例\n请重置筛选条件"),
        "no_mask_natural_bite":
            MessageLookupByLibrary.simpleMessage("自然咬合，从左向右扫描"),
        "no_mask_open_bimaxillary":
            MessageLookupByLibrary.simpleMessage("双颌微张，从左向右扫描"),
        "no_mask_scan_down": MessageLookupByLibrary.simpleMessage("张大嘴巴，扫描下颌"),
        "no_mask_scan_up": MessageLookupByLibrary.simpleMessage("张大嘴巴，扫描上颌"),
        "no_message": MessageLookupByLibrary.simpleMessage("暂时还没有消息记录"),
        "no_network": MessageLookupByLibrary.simpleMessage("网络不太顺畅，请检查网络设置"),
        "no_network_upload":
            MessageLookupByLibrary.simpleMessage("当前暂无网络，请联网后重试上传哦~"),
        "no_organization": MessageLookupByLibrary.simpleMessage("尚未填写工作单位"),
        "no_patient_data":
            MessageLookupByLibrary.simpleMessage("还没有病例哦\n请登录网页端灵芽创建"),
        "no_permission": MessageLookupByLibrary.simpleMessage("暂无权限"),
        "no_record": MessageLookupByLibrary.simpleMessage("暂无数据"),
        "no_scan_record": MessageLookupByLibrary.simpleMessage("暂无扫描记录"),
        "no_upload_fail_data":
            MessageLookupByLibrary.simpleMessage("当前无上传失败数据"),
        "no_usb_detected": MessageLookupByLibrary.simpleMessage("未检测到U盘"),
        "none": MessageLookupByLibrary.simpleMessage("无"),
        "normal_teeth_count": MessageLookupByLibrary.simpleMessage("正常牙齿\n%i颗"),
        "not_agree": MessageLookupByLibrary.simpleMessage("不同意"),
        "not_cancel_binding": MessageLookupByLibrary.simpleMessage("暂不解绑"),
        "not_login": MessageLookupByLibrary.simpleMessage("未登录"),
        "not_sorted": MessageLookupByLibrary.simpleMessage("未分类"),
        "nutritional_status": MessageLookupByLibrary.simpleMessage("营养状况"),
        "observe_plaque_pink_area": MessageLookupByLibrary.simpleMessage(
            "通过检测拍摄观察荧粉色的菌斑区域（常位于牙龈线、牙缝、牙齿舌侧、托槽周围等）"),
        "office": MessageLookupByLibrary.simpleMessage("科室"),
        "offline_search_activity_hint":
            MessageLookupByLibrary.simpleMessage("搜索活动名称/被采集人名字"),
        "ok": MessageLookupByLibrary.simpleMessage("好的"),
        "optional": MessageLookupByLibrary.simpleMessage("选填"),
        "optional_scan": MessageLookupByLibrary.simpleMessage("选拍"),
        "oralTrauma_history": MessageLookupByLibrary.simpleMessage("口腔外伤史"),
        "oral_hygiene": MessageLookupByLibrary.simpleMessage("口腔卫生"),
        "oral_tooth_extraction_history":
            MessageLookupByLibrary.simpleMessage("口腔拔牙史"),
        "order": MessageLookupByLibrary.simpleMessage("排序"),
        "order_desc": MessageLookupByLibrary.simpleMessage("降序"),
        "order_inc": MessageLookupByLibrary.simpleMessage("升序"),
        "organization": MessageLookupByLibrary.simpleMessage("医疗机构"),
        "other": MessageLookupByLibrary.simpleMessage("其他"),
        "other_exception": MessageLookupByLibrary.simpleMessage("其他异常"),
        "other_health_recommend":
            MessageLookupByLibrary.simpleMessage("其他健康建议"),
        "other_health_recommend_hint":
            MessageLookupByLibrary.simpleMessage("请输入内容，最多300字"),
        "other_info": MessageLookupByLibrary.simpleMessage("其他信息"),
        "other_question": MessageLookupByLibrary.simpleMessage("其他问题："),
        "overbite_deep_1": MessageLookupByLibrary.simpleMessage("轻度深覆𬌗 Ⅰ 度"),
        "overbite_deep_2": MessageLookupByLibrary.simpleMessage("中度深覆𬌗 Ⅱ 度"),
        "overbite_deep_3": MessageLookupByLibrary.simpleMessage("重度深覆𬌗 Ⅲ 度"),
        "overbite_normal": MessageLookupByLibrary.simpleMessage("正常覆𬌗"),
        "overbite_open_1": MessageLookupByLibrary.simpleMessage("开𬌗 Ⅰ 度"),
        "overbite_open_2": MessageLookupByLibrary.simpleMessage("开𬌗 Ⅱ 度"),
        "overbite_open_3": MessageLookupByLibrary.simpleMessage("开𬌗 Ⅲ 度"),
        "overbite_relationship": MessageLookupByLibrary.simpleMessage("覆𬌗关系"),
        "overjet_deep_1": MessageLookupByLibrary.simpleMessage("前牙深覆盖 Ⅰ 度"),
        "overjet_deep_2": MessageLookupByLibrary.simpleMessage("前牙深覆盖 Ⅱ 度"),
        "overjet_deep_3": MessageLookupByLibrary.simpleMessage("前牙深覆盖 Ⅲ 度"),
        "overjet_edge": MessageLookupByLibrary.simpleMessage("对刃"),
        "overjet_normal": MessageLookupByLibrary.simpleMessage("正常覆盖"),
        "overjet_relationship": MessageLookupByLibrary.simpleMessage("覆盖关系"),
        "pain": MessageLookupByLibrary.simpleMessage("疼痛"),
        "password_content_rule": MessageLookupByLibrary.simpleMessage("大小写字母"),
        "password_different": MessageLookupByLibrary.simpleMessage("密码不一致"),
        "password_different_from_old":
            MessageLookupByLibrary.simpleMessage("新旧密码不能相同"),
        "password_length_rule": MessageLookupByLibrary.simpleMessage("8-20个字符"),
        "password_rule":
            MessageLookupByLibrary.simpleMessage("密码长度8-20个字符，需包含大小写字母。"),
        "past_illness": MessageLookupByLibrary.simpleMessage("既往史"),
        "patient_detail": MessageLookupByLibrary.simpleMessage("患者详情"),
        "patient_info_and_image":
            MessageLookupByLibrary.simpleMessage("患者基本信息、图像"),
        "patient_info_and_pdf":
            MessageLookupByLibrary.simpleMessage("患者基本信息、图像和PDF报告"),
        "patient_remark": MessageLookupByLibrary.simpleMessage("患者备注"),
        "period_day": MessageLookupByLibrary.simpleMessage("天"),
        "period_month": MessageLookupByLibrary.simpleMessage("月"),
        "period_week": MessageLookupByLibrary.simpleMessage("周"),
        "periodontium": MessageLookupByLibrary.simpleMessage("牙周组织"),
        "permanent_tooth": MessageLookupByLibrary.simpleMessage("恒牙"),
        "personal_version": MessageLookupByLibrary.simpleMessage("灵芽个人版"),
        "phone": MessageLookupByLibrary.simpleMessage("联系电话"),
        "photos_for_report":
            MessageLookupByLibrary.simpleMessage("以下影像将用于制作报告"),
        "physical_examination": MessageLookupByLibrary.simpleMessage("体格检查"),
        "picker_day": MessageLookupByLibrary.simpleMessage("日"),
        "picker_month": MessageLookupByLibrary.simpleMessage("月"),
        "picker_year": MessageLookupByLibrary.simpleMessage("年"),
        "pickoff_mask": MessageLookupByLibrary.simpleMessage("取下牙套"),
        "pickoff_mask_natural_bite":
            MessageLookupByLibrary.simpleMessage("取下牙套，自然咬合，从左向右扫描"),
        "pickoff_mask_open_bimaxillary":
            MessageLookupByLibrary.simpleMessage("取下牙套，双颌微张，从左向右扫描"),
        "pickoff_mask_scan_down":
            MessageLookupByLibrary.simpleMessage("取下牙套，张大嘴巴，扫描下颌"),
        "pickoff_mask_scan_up":
            MessageLookupByLibrary.simpleMessage("取下牙套，张大嘴巴，扫描上颌"),
        "pickoff_mask_ware_mouth_prop":
            MessageLookupByLibrary.simpleMessage("请取下牙套，将开口器置入口中，然后继续"),
        "pickoff_mask_ware_mouth_prop2":
            MessageLookupByLibrary.simpleMessage("取下牙套，将开口器置入口中，然后继续"),
        "pickup_mask_open_bimaxillary":
            MessageLookupByLibrary.simpleMessage("戴上牙套，双颌微张，从左向右扫描"),
        "pickup_mask_ware_mouth_prop":
            MessageLookupByLibrary.simpleMessage("请戴上牙套，将开口器置入口中，然后继续"),
        "pickup_mask_ware_mouth_prop2":
            MessageLookupByLibrary.simpleMessage("戴上牙套，将开口器置入口中，然后继续"),
        "plaque": MessageLookupByLibrary.simpleMessage("牙菌斑"),
        "plaque_clean": MessageLookupByLibrary.simpleMessage("去除牙菌斑的方法："),
        "plaque_clean_details": MessageLookupByLibrary.simpleMessage(
            "良好的口腔卫生：定期刷牙（每天至少两次），使用含氟牙膏，以及每日使用牙线清洁牙缝。\n定期牙科检查：定期去牙科诊所进行专业清洁，可以去除难以自行清除的牙菌斑和牙石。\n饮食控制：减少糖分和精制碳水化合物的摄入，这些食物容易促进牙菌斑的形成。"),
        "plaque_danger": MessageLookupByLibrary.simpleMessage("牙菌斑的危害："),
        "plaque_danger_details": MessageLookupByLibrary.simpleMessage(
            "牙菌斑是牙周病和龋齿的主要原因。\n如果不及时清除，牙菌斑可以硬化成牙石，导致牙龈炎和牙周炎。\n长期的牙菌斑积累可能导致牙齿脱落、口腔异味和其他口腔健康问题。"),
        "plaque_detail_introduction": MessageLookupByLibrary.simpleMessage(
            "牙菌斑，也被称为牙斑或菌斑，是一种在牙齿表面形成的黏稠、无色的生物膜。它主要由在口腔中自然存在的细菌构成。"),
        "plaque_detect_history":
            MessageLookupByLibrary.simpleMessage("牙菌斑检测历史记录"),
        "plaque_exclude": MessageLookupByLibrary.simpleMessage(
            "以下特征大概率不是牙菌斑，而是龋齿、色素沉着等。牙菌斑会呈现荧粉色哦~"),
        "plaque_features": MessageLookupByLibrary.simpleMessage("以下特征的都是牙菌斑"),
        "plaque_generate": MessageLookupByLibrary.simpleMessage("牙菌斑的形成："),
        "plaque_generate_details": MessageLookupByLibrary.simpleMessage(
            "当食物残渣（特别是含糖食物）在口腔中残留时，它们会与口腔中的细菌结合。\n这些细菌利用食物残渣产生酸，随着时间的推移，这些酸会与唾液中的矿物质相结合，形成牙菌斑。\n牙菌斑通常首先在牙齿与牙龈接触的地方形成，随后扩散到整个牙齿表面。"),
        "plaque_open_bimaxillary":
            MessageLookupByLibrary.simpleMessage("牙菌斑检测，双颌微张，从左向右扫描"),
        "plaque_pink": MessageLookupByLibrary.simpleMessage("荧粉色"),
        "plaque_scan_down":
            MessageLookupByLibrary.simpleMessage("牙菌斑检测，张大嘴巴，扫描下颌"),
        "plaque_scan_up":
            MessageLookupByLibrary.simpleMessage("牙菌斑检测，张大嘴巴，扫描上颌"),
        "plaque_with_colors":
            MessageLookupByLibrary.simpleMessage("以下特征可能是龋齿、色素沉着的部位附着了牙菌斑"),
        "please_insert_usb":
            MessageLookupByLibrary.simpleMessage("请先插入U盘，再使用此功能。"),
        "please_read_and_agree":
            MessageLookupByLibrary.simpleMessage("请先勾选并同意《隐私政策》"),
        "please_read_privacy1": MessageLookupByLibrary.simpleMessage(
            "请您务必审慎阅读、充分理解\"服务协议\"和“隐私政策”各条款，包括但不限于：为了更好的向您提供服务，我们需要使用您的设备信息、使用情况数据和健康数据等。"),
        "please_read_privacy2": MessageLookupByLibrary.simpleMessage(
            "了解详细信息。如果您同意，请点击下面按钮开始接受我们的服务。"),
        "pre_ensure_indicator_light":
            MessageLookupByLibrary.simpleMessage("确保指示灯已亮。"),
        "pre_make_ultra_power_on":
            MessageLookupByLibrary.simpleMessage("长按设备的电源键开机，"),
        "premature_loss_baby_teeth":
            MessageLookupByLibrary.simpleMessage("乳牙早失"),
        "prepare_step": MessageLookupByLibrary.simpleMessage("前期准备"),
        "present_illness": MessageLookupByLibrary.simpleMessage("现病史"),
        "previous_step": MessageLookupByLibrary.simpleMessage("上一步"),
        "privacy": MessageLookupByLibrary.simpleMessage("隐私政策"),
        "progress_stage": MessageLookupByLibrary.simpleMessage("跟进阶段"),
        "protocal_and_privacy":
            MessageLookupByLibrary.simpleMessage("服务协议和隐私政策"),
        "put_front_tooth_center":
            MessageLookupByLibrary.simpleMessage("请将门牙居中"),
        "put_ultra_power_up":
            MessageLookupByLibrary.simpleMessage("请将电源键朝上，开口器放进嘴里"),
        "qcvm_judge": MessageLookupByLibrary.simpleMessage("QCVM判断方法"),
        "read_and_agree": MessageLookupByLibrary.simpleMessage("我已阅读并同意"),
        "recommend_by_ai": MessageLookupByLibrary.simpleMessage("AI推荐"),
        "recommend_by_ai_desc":
            MessageLookupByLibrary.simpleMessage("报告将根据AI分析自动生成推荐治疗项目"),
        "recommend_by_self": MessageLookupByLibrary.simpleMessage("自主推荐"),
        "recommend_treat_project":
            MessageLookupByLibrary.simpleMessage("推荐治疗项目"),
        "record_all_incomplete":
            MessageLookupByLibrary.simpleMessage("您有%i条扫描数据异常所以无法上传，请先处理异常数据。"),
        "record_empty_tip":
            MessageLookupByLibrary.simpleMessage("请查看页面下方的待上传列表\n或新建患者扫描"),
        "record_image_lost":
            MessageLookupByLibrary.simpleMessage("您有图片被系统清理了，请确认"),
        "record_part_incomplete": MessageLookupByLibrary.simpleMessage(
            "您有%i条扫描数据异常所以无法上传。 您可以先上传其他完整数据，再去“待上传”列表处理异常数据。"),
        "record_sn": MessageLookupByLibrary.simpleMessage("条形码"),
        "refetch_verify_code": MessageLookupByLibrary.simpleMessage("重新获取"),
        "register_and_login": MessageLookupByLibrary.simpleMessage("注册并登录"),
        "remake": MessageLookupByLibrary.simpleMessage("重新生成"),
        "remark": MessageLookupByLibrary.simpleMessage("备注"),
        "report_generate_fail": MessageLookupByLibrary.simpleMessage("报告生成失败"),
        "report_generate_going": MessageLookupByLibrary.simpleMessage("报告正在生成"),
        "report_generate_success":
            MessageLookupByLibrary.simpleMessage("报告生成成功"),
        "report_name": MessageLookupByLibrary.simpleMessage("报告名称"),
        "report_preview": MessageLookupByLibrary.simpleMessage("报告预览"),
        "report_show_ts_logo":
            MessageLookupByLibrary.simpleMessage("报告封面显示乐齿拍Logo"),
        "report_status_fail": MessageLookupByLibrary.simpleMessage("生成失败"),
        "report_status_making": MessageLookupByLibrary.simpleMessage("正在生成"),
        "report_status_outdate": MessageLookupByLibrary.simpleMessage("已过期"),
        "request_camera_permission": MessageLookupByLibrary.simpleMessage(
            "灵芽需要请求您的相机权限，用于拍摄面像照，请前往手机系统设置打开相机权限"),
        "request_camera_qrcode":
            MessageLookupByLibrary.simpleMessage("灵芽需要请求您的相机权限\n用于扫描条形码"),
        "request_camera_scan":
            MessageLookupByLibrary.simpleMessage("灵芽需要请求您的相机权限\n用于拍摄口腔图像"),
        "request_camera_smile":
            MessageLookupByLibrary.simpleMessage("灵芽需要请求您的相机权限\n用于拍摄面像照"),
        "request_data_lost": MessageLookupByLibrary.simpleMessage("您有必填项未填"),
        "request_storage":
            MessageLookupByLibrary.simpleMessage("灵芽需要请求您的存储权限\n用于保存图片"),
        "request_storage_permission":
            MessageLookupByLibrary.simpleMessage("请求系统存储权限失败，请手动授权"),
        "rescan_binding": MessageLookupByLibrary.simpleMessage("重新扫描"),
        "reset": MessageLookupByLibrary.simpleMessage("重置"),
        "reset_password": MessageLookupByLibrary.simpleMessage("重置密码"),
        "reset_password_success":
            MessageLookupByLibrary.simpleMessage("重置密码成功"),
        "reset_password_successful":
            MessageLookupByLibrary.simpleMessage("密码重置成功，请重新登录"),
        "resolve_action": MessageLookupByLibrary.simpleMessage("解决办法"),
        "retake_all_photos": MessageLookupByLibrary.simpleMessage("重拍全部"),
        "retake_current_record": MessageLookupByLibrary.simpleMessage("重拍本段"),
        "retake_current_scan": MessageLookupByLibrary.simpleMessage("重拍此段"),
        "retake_photo": MessageLookupByLibrary.simpleMessage("重新拍摄"),
        "retention_baby_teeth": MessageLookupByLibrary.simpleMessage("乳牙滞留"),
        "retry": MessageLookupByLibrary.simpleMessage("重试"),
        "retry_analysis": MessageLookupByLibrary.simpleMessage("重新分析"),
        "retry_connect": MessageLookupByLibrary.simpleMessage("重试连接"),
        "retry_scan": MessageLookupByLibrary.simpleMessage("重新搜索"),
        "retry_upload": MessageLookupByLibrary.simpleMessage("重新上传"),
        "retry_upload_all": MessageLookupByLibrary.simpleMessage("一键重新上传"),
        "retry_upload_failed_record":
            MessageLookupByLibrary.simpleMessage("如果有上传失败的扫描记录，请点击重新上传"),
        "right_side": MessageLookupByLibrary.simpleMessage("右侧"),
        "right_tooth": MessageLookupByLibrary.simpleMessage("右牙"),
        "s_size": MessageLookupByLibrary.simpleMessage("S号"),
        "save": MessageLookupByLibrary.simpleMessage("保存"),
        "save_and_add_next": MessageLookupByLibrary.simpleMessage("保存并新建"),
        "save_and_exit": MessageLookupByLibrary.simpleMessage("保存并退出"),
        "save_and_return": MessageLookupByLibrary.simpleMessage("暂存并返回"),
        "save_info": MessageLookupByLibrary.simpleMessage("是否需要保存当前患者信息"),
        "save_success": MessageLookupByLibrary.simpleMessage("保存成功"),
        "save_to_gallery": MessageLookupByLibrary.simpleMessage("已保存到系统相册"),
        "saved_to_upload_list":
            MessageLookupByLibrary.simpleMessage("已保存，可在待上传中查看"),
        "scan": MessageLookupByLibrary.simpleMessage("扫描"),
        "scan_activity_list":
            MessageLookupByLibrary.simpleMessage("口腔数据采集活动列表"),
        "scan_activity_source": MessageLookupByLibrary.simpleMessage("活动来源"),
        "scan_bind_code": MessageLookupByLibrary.simpleMessage("扫描绑定条形码"),
        "scan_blur_action":
            MessageLookupByLibrary.simpleMessage("1.建议安装适配器\n2.清洁手机摄像头"),
        "scan_blur_action_ultra":
            MessageLookupByLibrary.simpleMessage("跟随箭头匀速移动开口器，速度不要过快"),
        "scan_blur_focus_fail":
            MessageLookupByLibrary.simpleMessage("画面模糊，建议使用适配器以便对焦"),
        "scan_blur_keep_speed":
            MessageLookupByLibrary.simpleMessage("画面模糊，请匀速扫描"),
        "scan_center_front":
            MessageLookupByLibrary.simpleMessage("当箭头移到中间时，门牙应该居中"),
        "scan_code": MessageLookupByLibrary.simpleMessage("扫描条形码"),
        "scan_convert_project":
            MessageLookupByLibrary.simpleMessage("口采阶段治疗方向"),
        "scan_data_file": MessageLookupByLibrary.simpleMessage("口采数据文件"),
        "scan_data_file_desc1":
            MessageLookupByLibrary.simpleMessage("这是口腔数据采集模块存在本地的备份，清理之后，"),
        "scan_data_file_desc2": MessageLookupByLibrary.simpleMessage(
            "已上传的数据可以正常使用，待上传、上传中和上传失败的数据彻底被清空，不可恢复。\n"),
        "scan_data_file_desc3":
            MessageLookupByLibrary.simpleMessage("请确保所有必要数据已上传成功之后再来清理。"),
        "scan_detail": MessageLookupByLibrary.simpleMessage("扫描详情"),
        "scan_device_fail": MessageLookupByLibrary.simpleMessage("搜不到设备？"),
        "scan_direction_error": MessageLookupByLibrary.simpleMessage("扫描方向错误"),
        "scan_down_bat": MessageLookupByLibrary.simpleMessage("方向错误，请扫描下牙的咬合面"),
        "scan_down_maxillary":
            MessageLookupByLibrary.simpleMessage("牙弓角度不足，请将开口器尽可能朝下"),
        "scan_example": MessageLookupByLibrary.simpleMessage("扫描示范"),
        "scan_follow_progress_center":
            MessageLookupByLibrary.simpleMessage("当箭头移到中间时，门牙应该居中"),
        "scan_great": MessageLookupByLibrary.simpleMessage("扫描得很棒"),
        "scan_in_hospital": MessageLookupByLibrary.simpleMessage("单人采集"),
        "scan_in_hospital_desc":
            MessageLookupByLibrary.simpleMessage("无需创建活动，适用于单个患者快速采集"),
        "scan_intraoral_photo": MessageLookupByLibrary.simpleMessage("口内照录入"),
        "scan_iscan_into_mouth_down":
            MessageLookupByLibrary.simpleMessage("嘴巴张到最大，将开口器深入口腔并完全向下"),
        "scan_iscan_into_mouth_up":
            MessageLookupByLibrary.simpleMessage("嘴巴张到最大，将开口器深入口腔并完全向上"),
        "scan_keep_bite_back_teeth":
            MessageLookupByLibrary.simpleMessage("咽口水，找到自然咬合的感觉，全程保持"),
        "scan_keep_gently_stretch_teeth":
            MessageLookupByLibrary.simpleMessage("正确的幅度如图所示，上下牙不能互相遮挡"),
        "scan_keep_gently_stretch_teeth_to_doctor":
            MessageLookupByLibrary.simpleMessage("正确的幅度如图所示，注意牙根不要被遮挡"),
        "scan_left_to_right":
            MessageLookupByLibrary.simpleMessage("开口器推向箭头所指方向，直到看清最后一颗牙齿"),
        "scan_no_behind_tooth":
            MessageLookupByLibrary.simpleMessage("未扫描到完整的后牙"),
        "scan_no_device": MessageLookupByLibrary.simpleMessage("找不到设备"),
        "scan_out_hospital": MessageLookupByLibrary.simpleMessage("批量采集"),
        "scan_out_hospital_desc":
            MessageLookupByLibrary.simpleMessage("先创建活动再采集，适用于批量扫描活动"),
        "scan_plaque": MessageLookupByLibrary.simpleMessage("牙菌斑检测"),
        "scan_record": MessageLookupByLibrary.simpleMessage("扫描记录"),
        "scan_result": MessageLookupByLibrary.simpleMessage("搜索到"),
        "scan_result_blur": MessageLookupByLibrary.simpleMessage("扫描结果模糊"),
        "scan_skill": MessageLookupByLibrary.simpleMessage("拍摄技巧"),
        "scan_time": MessageLookupByLibrary.simpleMessage("口采时间"),
        "scan_to_record": MessageLookupByLibrary.simpleMessage("扫描录入"),
        "scan_try_open_mouth_down":
            MessageLookupByLibrary.simpleMessage("扫描时稍微低头，开口器尽可能朝下"),
        "scan_try_open_mouth_up":
            MessageLookupByLibrary.simpleMessage("扫描时稍微抬头，开口器尽可能朝上"),
        "scan_turn_head_behind_tooth":
            MessageLookupByLibrary.simpleMessage("扫描时，头向反方向转，更容易扫到后牙"),
        "scan_ultra_tip_content11":
            MessageLookupByLibrary.simpleMessage("处于开机状态"),
        "scan_ultra_tip_content12":
            MessageLookupByLibrary.simpleMessage("不要离手机太远"),
        "scan_ultra_tip_content13": MessageLookupByLibrary.simpleMessage(
            "电量充足，指示灯为绿色\n（电量不足，指示灯为红\n色，建议充电后再使用）"),
        "scan_ultra_tip_content21":
            MessageLookupByLibrary.simpleMessage("长按关闭设备，再次长按开\n启并连接"),
        "scan_ultra_tip_content22":
            MessageLookupByLibrary.simpleMessage("彻底关闭灵芽应用程序，\n再次打开"),
        "scan_ultra_tip_content23":
            MessageLookupByLibrary.simpleMessage("如遇设备死机，请插入充电线\n再拔下即可恢复"),
        "scan_ultra_tip_content31":
            MessageLookupByLibrary.simpleMessage("请联系客服：19957156823"),
        "scan_ultra_tip_content32":
            MessageLookupByLibrary.simpleMessage("请扫描客服微信二维码"),
        "scan_ultra_tip_content4": MessageLookupByLibrary.simpleMessage(
            "电量充足，指示灯为绿色（电量不足，指\n示灯为红色，建议充电后再使用）"),
        "scan_ultra_tip_title1": MessageLookupByLibrary.simpleMessage("请确保设备："),
        "scan_ultra_tip_title2":
            MessageLookupByLibrary.simpleMessage("如果仍然搜不到，请尝试："),
        "scan_ultra_tip_title3":
            MessageLookupByLibrary.simpleMessage("如果以上方法均无效："),
        "scan_up_bat": MessageLookupByLibrary.simpleMessage("方向错误，请扫描上牙的咬合面"),
        "scan_up_maxillary":
            MessageLookupByLibrary.simpleMessage("牙弓角度不足，请将开口器尽可能朝上"),
        "scan_while_down_maxillary":
            MessageLookupByLibrary.simpleMessage("下牙的咬合面未扫描完整"),
        "scan_whole_up_maxillary":
            MessageLookupByLibrary.simpleMessage("上牙的咬合面未扫描完整"),
        "screen_light": MessageLookupByLibrary.simpleMessage("屏幕亮度"),
        "search": MessageLookupByLibrary.simpleMessage("搜索"),
        "search_activity_hint":
            MessageLookupByLibrary.simpleMessage("搜索活动名称/归属人/被采集人名字"),
        "search_by_activity_name": MessageLookupByLibrary.simpleMessage("按活动名"),
        "search_by_person_name": MessageLookupByLibrary.simpleMessage("按归属人"),
        "search_by_record_name": MessageLookupByLibrary.simpleMessage("按被采集人"),
        "search_dot": MessageLookupByLibrary.simpleMessage("搜索中......"),
        "search_record_hint":
            MessageLookupByLibrary.simpleMessage("搜索“%s”中的被采集人"),
        "search_scan_hint": MessageLookupByLibrary.simpleMessage("请输入患者姓名或手机号"),
        "select": MessageLookupByLibrary.simpleMessage("选择"),
        "select_birthday": MessageLookupByLibrary.simpleMessage("请点击选择出生日期"),
        "select_contract": MessageLookupByLibrary.simpleMessage("疗程选择"),
        "select_record": MessageLookupByLibrary.simpleMessage("选择监控记录"),
        "select_record_count": MessageLookupByLibrary.simpleMessage("已选%d个"),
        "select_scan_mode": MessageLookupByLibrary.simpleMessage("请选择您如何进行扫描？"),
        "select_usb_save": MessageLookupByLibrary.simpleMessage("选择U盘保存"),
        "send_feedback": MessageLookupByLibrary.simpleMessage("发送反馈"),
        "send_verify_code_fail":
            MessageLookupByLibrary.simpleMessage("发送失败,请重新获取验证码"),
        "set_all_read": MessageLookupByLibrary.simpleMessage("全部已读"),
        "set_new_password": MessageLookupByLibrary.simpleMessage("设置新密码"),
        "setting_about": MessageLookupByLibrary.simpleMessage("关于乐齿拍"),
        "setting_contact": MessageLookupByLibrary.simpleMessage("联系我们"),
        "setting_language": MessageLookupByLibrary.simpleMessage("语言设置"),
        "setting_message": MessageLookupByLibrary.simpleMessage("消息通知设置"),
        "setting_secure": MessageLookupByLibrary.simpleMessage("安全设置"),
        "setting_system": MessageLookupByLibrary.simpleMessage("系统设置"),
        "setup_direction": MessageLookupByLibrary.simpleMessage("装入方向"),
        "setup_iscan_tip1": MessageLookupByLibrary.simpleMessage("成人用-灰色"),
        "setup_iscan_tip2": MessageLookupByLibrary.simpleMessage("儿童用-白色"),
        "setup_iscan_tip3":
            MessageLookupByLibrary.simpleMessage("如有明显不适感，可以用-黄色"),
        "setup_mouth_prop_tip":
            MessageLookupByLibrary.simpleMessage("将开口器置入口中并对准牙齿，摄像头不被遮挡"),
        "setup_scanner_on_ultra":
            MessageLookupByLibrary.simpleMessage("将开口器安装在设备机身上。"),
        "setup_start_adjust": MessageLookupByLibrary.simpleMessage("组装好了，开始校准"),
        "setup_ultra_start": MessageLookupByLibrary.simpleMessage("我已开启设备，下一步"),
        "setup_ultra_tip1": MessageLookupByLibrary.simpleMessage("成人用-中号开口器"),
        "setup_ultra_tip2": MessageLookupByLibrary.simpleMessage("儿童用-小号开口器"),
        "setup_volume_for_scan":
            MessageLookupByLibrary.simpleMessage("打开声音，根据语音提示扫描，有助于更准确地完成扫描。"),
        "shut_off_ultra_exit": MessageLookupByLibrary.simpleMessage(
            "请先连接至可用的网络（把手机WiFi关闭再开启，或使用手机流量），并且长按设备关机。"),
        "shut_off_ultra_submit": MessageLookupByLibrary.simpleMessage(
            "请连接至可用的网络（把手机WiFi关闭再开启，或使用手机流量），并且长按设备关机，然后再上传"),
        "side": MessageLookupByLibrary.simpleMessage("侧面"),
        "simple_date": MessageLookupByLibrary.simpleMessage("%s-%s-%s"),
        "skip": MessageLookupByLibrary.simpleMessage("跳过"),
        "smile_fullness": MessageLookupByLibrary.simpleMessage("微笑丰满度"),
        "smile_width": MessageLookupByLibrary.simpleMessage("微笑宽度"),
        "sort_tags": MessageLookupByLibrary.simpleMessage("分类标签"),
        "sound": MessageLookupByLibrary.simpleMessage("弹响"),
        "space_teeth": MessageLookupByLibrary.simpleMessage("缺牙间隙"),
        "speed_forward": MessageLookupByLibrary.simpleMessage("倍速 快进中"),
        "stage": MessageLookupByLibrary.simpleMessage("分期"),
        "stage_phase": MessageLookupByLibrary.simpleMessage("阶段名称"),
        "stage_profile": MessageLookupByLibrary.simpleMessage("形态参考标准"),
        "standard_scan": MessageLookupByLibrary.simpleMessage("标准扫描"),
        "start_detect": MessageLookupByLibrary.simpleMessage("开始检测"),
        "start_scan": MessageLookupByLibrary.simpleMessage("开始扫描"),
        "start_time": MessageLookupByLibrary.simpleMessage("开始时间"),
        "status": MessageLookupByLibrary.simpleMessage("状态"),
        "status_bound": MessageLookupByLibrary.simpleMessage("暂无记录"),
        "status_complete_upload": MessageLookupByLibrary.simpleMessage("已上传"),
        "status_deleted": MessageLookupByLibrary.simpleMessage("已归档"),
        "status_destroy": MessageLookupByLibrary.simpleMessage("已注销"),
        "status_fail_upload": MessageLookupByLibrary.simpleMessage("上传失败"),
        "status_normal": MessageLookupByLibrary.simpleMessage("正常"),
        "status_not_binding": MessageLookupByLibrary.simpleMessage("未绑定"),
        "status_overtime": MessageLookupByLibrary.simpleMessage("已逾期"),
        "status_processing": MessageLookupByLibrary.simpleMessage("正常进行"),
        "status_recording": MessageLookupByLibrary.simpleMessage("待完善"),
        "status_uploading": MessageLookupByLibrary.simpleMessage("正在上传"),
        "status_waiting_upload": MessageLookupByLibrary.simpleMessage("待上传"),
        "step_index": MessageLookupByLibrary.simpleMessage("第%i步"),
        "stretch": MessageLookupByLibrary.simpleMessage("拉伸"),
        "submitting_files": MessageLookupByLibrary.simpleMessage("正在上传……"),
        "suggest_front_camera":
            MessageLookupByLibrary.simpleMessage("建议使用前置摄像头"),
        "suggest_treat_to_fill":
            MessageLookupByLibrary.simpleMessage("建议有早矫治疗意向的填写"),
        "surface_slices": MessageLookupByLibrary.simpleMessage("曲面断层片"),
        "suspended": MessageLookupByLibrary.simpleMessage("疑似"),
        "switch_area": MessageLookupByLibrary.simpleMessage("切换地区"),
        "switch_device_to_light": MessageLookupByLibrary.simpleMessage(
            "请您关闭当前设备，并开启一台 Light Wi-Fi扫描装置，然后才能进行牙菌斑检测"),
        "switch_success": MessageLookupByLibrary.simpleMessage("切换成功"),
        "switch_tenant": MessageLookupByLibrary.simpleMessage("切换机构"),
        "system_announce": MessageLookupByLibrary.simpleMessage("系统公告"),
        "system_audio": MessageLookupByLibrary.simpleMessage("系统音量"),
        "tag": MessageLookupByLibrary.simpleMessage("标签"),
        "take_photo": MessageLookupByLibrary.simpleMessage("拍照"),
        "teeth": MessageLookupByLibrary.simpleMessage("牙性"),
        "teeth_good": MessageLookupByLibrary.simpleMessage("情况良好"),
        "teeth_list_good": MessageLookupByLibrary.simpleMessage("未发现明显牙列问题"),
        "teeth_lost": MessageLookupByLibrary.simpleMessage("牙体缺失"),
        "teeth_photos": MessageLookupByLibrary.simpleMessage("牙颌模型"),
        "tenant_cannot_destroy_account":
            MessageLookupByLibrary.simpleMessage("必须退出所有机构才可以注销，请先在网页端退出机构!"),
        "tenant_qrcode_size_limit":
            MessageLookupByLibrary.simpleMessage("文件大小不能超过2M"),
        "tenant_worker": MessageLookupByLibrary.simpleMessage("机构人员"),
        "tenant_worker_address":
            MessageLookupByLibrary.simpleMessage("机构门诊地址："),
        "tenant_worker_name": MessageLookupByLibrary.simpleMessage("机构人员姓名："),
        "tenant_worker_phone": MessageLookupByLibrary.simpleMessage("机构人员电话："),
        "tenant_worker_qrcode":
            MessageLookupByLibrary.simpleMessage("机构人员二维码："),
        "tenant_worker_qrcode_desc":
            MessageLookupByLibrary.simpleMessage("二维码说明："),
        "tenant_worker_qrcode_tip":
            MessageLookupByLibrary.simpleMessage("上传图片的长宽比最好接近1:1，且文件应小于2M"),
        "terminate_export": MessageLookupByLibrary.simpleMessage("终止传输"),
        "terminate_export_confirm":
            MessageLookupByLibrary.simpleMessage("是否确认终止传输？"),
        "time": MessageLookupByLibrary.simpleMessage("时间"),
        "time_desc": MessageLookupByLibrary.simpleMessage("时间倒序"),
        "time_sort": MessageLookupByLibrary.simpleMessage("时间正序"),
        "tip": MessageLookupByLibrary.simpleMessage("提示"),
        "tip_use_web_lingya":
            MessageLookupByLibrary.simpleMessage("如需机构版的完整功能，请使用网页版灵芽"),
        "tooth_around": MessageLookupByLibrary.simpleMessage("牙周"),
        "tooth_blur": MessageLookupByLibrary.simpleMessage("牙齿模糊"),
        "tooth_body": MessageLookupByLibrary.simpleMessage("牙体、牙周"),
        "tooth_body_ai_tip": MessageLookupByLibrary.simpleMessage(
            "AI目前仅支持识别龋齿、牙结石、色素沉着、残根、残冠、楔状缺损、脱矿、软垢、牙周炎(包含牙龈退缩/牙龈红肿/牙龈出血三种表象)"),
        "tooth_expose_too_less":
            MessageLookupByLibrary.simpleMessage("未检测到牙齿或暴露出来的牙齿太少"),
        "tooth_list": MessageLookupByLibrary.simpleMessage("牙列"),
        "tooth_list_ai_tip": MessageLookupByLibrary.simpleMessage(
            "AI目前仅支持识别牙列不齐（颌面观的牙列流畅度不佳）、牙列间隙、牙号缺失、缺牙间隙"),
        "tooth_overbite": MessageLookupByLibrary.simpleMessage("覆𬌗"),
        "tooth_overjet": MessageLookupByLibrary.simpleMessage("覆盖"),
        "total_contract_count": MessageLookupByLibrary.simpleMessage("共%i个疗程"),
        "total_patient_count": MessageLookupByLibrary.simpleMessage("患者总数"),
        "total_record_count": MessageLookupByLibrary.simpleMessage("共%i条记录"),
        "treatment": MessageLookupByLibrary.simpleMessage("治疗方案"),
        "try_again": MessageLookupByLibrary.simpleMessage("请稍后再试"),
        "turn_on_wifi": MessageLookupByLibrary.simpleMessage("开启Wi-Fi"),
        "turn_on_wifi_phone": MessageLookupByLibrary.simpleMessage("打开手机Wi-Fi"),
        "type_100": MessageLookupByLibrary.simpleMessage("正面像"),
        "type_100_1": MessageLookupByLibrary.simpleMessage("正面照"),
        "type_101": MessageLookupByLibrary.simpleMessage("侧面像"),
        "type_101_1": MessageLookupByLibrary.simpleMessage("侧面照"),
        "type_102": MessageLookupByLibrary.simpleMessage("正面微笑像"),
        "type_102_1": MessageLookupByLibrary.simpleMessage("微笑照"),
        "type_103": MessageLookupByLibrary.simpleMessage("正面咬合像"),
        "type_104": MessageLookupByLibrary.simpleMessage("正面咬合45°像"),
        "type_105": MessageLookupByLibrary.simpleMessage("左侧咬合像"),
        "type_106": MessageLookupByLibrary.simpleMessage("下牙弓像"),
        "type_107": MessageLookupByLibrary.simpleMessage("右侧咬合像"),
        "type_108": MessageLookupByLibrary.simpleMessage("上牙弓像"),
        "type_109": MessageLookupByLibrary.simpleMessage("全颌曲面断层片"),
        "type_110": MessageLookupByLibrary.simpleMessage("头颅侧位定位片"),
        "type_111": MessageLookupByLibrary.simpleMessage("覆盖像"),
        "type_112": MessageLookupByLibrary.simpleMessage("侧面45度像"),
        "type_113": MessageLookupByLibrary.simpleMessage("右侧45°"),
        "type_114": MessageLookupByLibrary.simpleMessage("右侧面微笑像"),
        "type_115": MessageLookupByLibrary.simpleMessage("左45度微笑像"),
        "type_116": MessageLookupByLibrary.simpleMessage("左侧45°"),
        "type_117": MessageLookupByLibrary.simpleMessage("左侧面微笑像"),
        "type_118": MessageLookupByLibrary.simpleMessage("左侧面像"),
        "type_118_1": MessageLookupByLibrary.simpleMessage("左侧90°"),
        "type_119": MessageLookupByLibrary.simpleMessage("右侧面像"),
        "type_119_1": MessageLookupByLibrary.simpleMessage("右侧90°"),
        "type_120": MessageLookupByLibrary.simpleMessage("右45度微笑像"),
        "type_151": MessageLookupByLibrary.simpleMessage("口内开颌正位像"),
        "type_152": MessageLookupByLibrary.simpleMessage("口内开颌左位像"),
        "type_153": MessageLookupByLibrary.simpleMessage("口内开颌右位像"),
        "type_154": MessageLookupByLibrary.simpleMessage("口内牙套正位像"),
        "type_155": MessageLookupByLibrary.simpleMessage("口内牙套左位像"),
        "type_156": MessageLookupByLibrary.simpleMessage("口内牙套右位像"),
        "type_200": MessageLookupByLibrary.simpleMessage("上颌模型"),
        "type_201": MessageLookupByLibrary.simpleMessage("下颌模型"),
        "type_300": MessageLookupByLibrary.simpleMessage("CBCT"),
        "ultra_device_not_support": MessageLookupByLibrary.simpleMessage(
            "抱歉，您的MOOELI Ultra设备版本较低，无法连接灵芽App，详情可咨询客服。"),
        "ultra_faq_block_connecting":
            MessageLookupByLibrary.simpleMessage("卡在“连接中...”怎么办？"),
        "ultra_faq_charge":
            MessageLookupByLibrary.simpleMessage("若无法关闭设备，请插入充电线再拔下即可恢复"),
        "ultra_faq_check_light":
            MessageLookupByLibrary.simpleMessage("检查设备指示灯是否为绿色闪烁状态"),
        "ultra_faq_check_wifi":
            MessageLookupByLibrary.simpleMessage("查看wifi是否开启"),
        "ultra_faq_check_wifi_ios": MessageLookupByLibrary.simpleMessage(
            "查看wifi是否开启，且是否允许灵芽App访问本地网络（设置--隐私--本地网络--灵芽权限开启）"),
        "ultra_faq_connect_wifi": MessageLookupByLibrary.simpleMessage(
            "若仍然无法连接，请至手机wifi列表手动连接%s（密码：Ultra001），并切换回灵芽App"),
        "ultra_faq_ignore_wifi":
            MessageLookupByLibrary.simpleMessage("在手机wifi列表中打开%s，并选择忽略此网络"),
        "ultra_faq_light_long":
            MessageLookupByLibrary.simpleMessage("若指示灯为常亮状态，长按关闭设备，再次长按开启并连接"),
        "ultra_faq_light_red":
            MessageLookupByLibrary.simpleMessage("若指示灯为红色，请充电"),
        "ultra_faq_not_resolve": MessageLookupByLibrary.simpleMessage("若无法解决"),
        "ultra_faq_restart_ultra":
            MessageLookupByLibrary.simpleMessage("退出连接，长按关闭设备，再次长按开启并连接"),
        "ultra_faq_retry_first": MessageLookupByLibrary.simpleMessage("重试第一步"),
        "ultra_faq_stop_vpn":
            MessageLookupByLibrary.simpleMessage("断开VPN等代理连接"),
        "ultra_faq_two_wlan":
            MessageLookupByLibrary.simpleMessage("确保双WLAN加速模式关闭"),
        "ultra_handle_connect1": MessageLookupByLibrary.simpleMessage(
            "若搜索不到，请至手机WiFi列表手动连接%s（密码：Ultra001 "),
        "ultra_handle_connect2":
            MessageLookupByLibrary.simpleMessage("），并切换回灵芽App"),
        "ultra_near_phone": MessageLookupByLibrary.simpleMessage("请将设备靠近手机"),
        "unit_count": MessageLookupByLibrary.simpleMessage("份"),
        "unit_counts": MessageLookupByLibrary.simpleMessage("份"),
        "unit_team": MessageLookupByLibrary.simpleMessage("组"),
        "unit_teams": MessageLookupByLibrary.simpleMessage("组"),
        "unread_message_count": MessageLookupByLibrary.simpleMessage("%i条未读消息"),
        "update_time": MessageLookupByLibrary.simpleMessage("更新时间"),
        "upload": MessageLookupByLibrary.simpleMessage("上传"),
        "upload_all": MessageLookupByLibrary.simpleMessage("全部上传"),
        "upload_fail": MessageLookupByLibrary.simpleMessage("上传失败，请重新尝试..."),
        "upload_list_count":
            MessageLookupByLibrary.simpleMessage("正在上传: %i    已上传: %i"),
        "upload_other_complete_data":
            MessageLookupByLibrary.simpleMessage("先上传其他完整数据"),
        "upload_record": MessageLookupByLibrary.simpleMessage("上传记录"),
        "upload_record_time_tip":
            MessageLookupByLibrary.simpleMessage("每条数据上传约3秒，请耐心等待"),
        "upload_records_first":
            MessageLookupByLibrary.simpleMessage("为确保数据完整性，请先点击全部上传，然后再继续添加患者"),
        "upload_result_fail": MessageLookupByLibrary.simpleMessage("上传失败"),
        "upload_result_success": MessageLookupByLibrary.simpleMessage("上传成功"),
        "upload_to_cloud_ing": MessageLookupByLibrary.simpleMessage("正在上传中"),
        "upload_to_cloud_success":
            MessageLookupByLibrary.simpleMessage("已上传到云端"),
        "upload_today":
            MessageLookupByLibrary.simpleMessage("提示：为确保数据完整，活动结束后请立刻上传"),
        "uploading": MessageLookupByLibrary.simpleMessage("上传中"),
        "usb_export_content_tip":
            MessageLookupByLibrary.simpleMessage("请选择导出到U盘的内容："),
        "usb_export_progress": MessageLookupByLibrary.simpleMessage(
            "整体传输进度：已导出%s个，导出中%s个，导出失败%s个"),
        "usb_export_progress_title":
            MessageLookupByLibrary.simpleMessage("传输进度"),
        "usb_export_terminated": MessageLookupByLibrary.simpleMessage("传输已终止"),
        "usb_recognized":
            MessageLookupByLibrary.simpleMessage("未识别到u盘，请确认u盘已经插好！"),
        "use_different_mail": MessageLookupByLibrary.simpleMessage("新旧邮箱不能相同"),
        "use_ultra_question": MessageLookupByLibrary.simpleMessage("使用遇到问题？"),
        "user_agreement": MessageLookupByLibrary.simpleMessage("服务协议"),
        "user_profile": MessageLookupByLibrary.simpleMessage("个人信息"),
        "verify_code_sent": MessageLookupByLibrary.simpleMessage("验证码已发送"),
        "version": MessageLookupByLibrary.simpleMessage("当前版本"),
        "version_upgrade": MessageLookupByLibrary.simpleMessage("版本更新"),
        "video": MessageLookupByLibrary.simpleMessage("视频"),
        "view_block_answer": MessageLookupByLibrary.simpleMessage("请重启扫描设备"),
        "view_block_question":
            MessageLookupByLibrary.simpleMessage("摄像头画面卡住怎么办？"),
        "view_detail": MessageLookupByLibrary.simpleMessage("查看详情"),
        "view_download_report":
            MessageLookupByLibrary.simpleMessage("您可于【我的-我的文件】处查看并下载文件"),
        "view_error_data": MessageLookupByLibrary.simpleMessage("查看异常数据"),
        "view_feedback": MessageLookupByLibrary.simpleMessage("查看反馈"),
        "view_mooeli_monitor": MessageLookupByLibrary.simpleMessage("查看监控详情"),
        "waiting_new_function": MessageLookupByLibrary.simpleMessage("敬请期待"),
        "warn": MessageLookupByLibrary.simpleMessage("提醒"),
        "wear_mask_open_maxillary":
            MessageLookupByLibrary.simpleMessage("牙齿张开幅度如图所示"),
        "weight": MessageLookupByLibrary.simpleMessage("体重"),
        "welcome_login_lyoral": MessageLookupByLibrary.simpleMessage("欢迎使用灵芽"),
        "welcome_register_lyoral":
            MessageLookupByLibrary.simpleMessage("欢迎注册灵芽"),
        "what_is_plaque": MessageLookupByLibrary.simpleMessage("什么是牙菌斑？"),
        "wlan": MessageLookupByLibrary.simpleMessage("WLAN"),
        "x_photos": MessageLookupByLibrary.simpleMessage("X光片"),
        "xray_airway": MessageLookupByLibrary.simpleMessage("气道分析"),
        "xray_analysis": MessageLookupByLibrary.simpleMessage("X线片分析"),
        "xray_bone": MessageLookupByLibrary.simpleMessage("骨龄分析"),
        "xray_lateral": MessageLookupByLibrary.simpleMessage("头影分析"),
        "year_old": MessageLookupByLibrary.simpleMessage("%s岁"),
        "you_can": MessageLookupByLibrary.simpleMessage("您可以:"),
        "you_can_read": MessageLookupByLibrary.simpleMessage("您可阅读"),
        "you_have_no_treatment":
            MessageLookupByLibrary.simpleMessage("您还没有加入任何疗程"),
        "your_scan_result": MessageLookupByLibrary.simpleMessage("您的扫描"),
        "zip_error": MessageLookupByLibrary.simpleMessage("拍摄出了点问题，请重拍本段")
      };
}

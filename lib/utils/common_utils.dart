import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:mooeli/common/global.dart';
import 'package:mooeli/common/http.dart';
import 'package:mooeli/manager/languaga_manager.dart';
import 'package:mooeli/user/user.dart';
import 'package:mooeli/utils/colors_utils.dart';
import 'package:mooeli/widget/my_widget.dart';
import 'package:path_provider/path_provider.dart';

typedef EmptyCallback = void Function();

typedef TypeCallback<T> = void Function(T result);

typedef TwoTypeCallback<T, R> = void Function(T result1, R result2);

typedef ResultCallback<T, R> = R Function(T result);

var _logger = Logger(
  printer: LongPrettyPrinter(
    colors: false,
    methodCount: 0,
    lineLength: 2000,
    warpLen: 300,
  ),
);

class LongPrettyPrinter extends PrettyPrinter {
  final int warpLen; //控制换行个数

  @override
  LongPrettyPrinter({
    this.warpLen = 1000,
    stackTraceBeginIndex = 0,
    methodCount = 2,
    errorMethodCount = 8,
    lineLength = 120,
    colors = true,
    printEmojis = true,
    printTime = false,
    noBoxingByDefault = false,
  }) : super(
          stackTraceBeginIndex: stackTraceBeginIndex,
          methodCount: methodCount,
          errorMethodCount: errorMethodCount,
          lineLength: lineLength,
          colors: colors,
          printEmojis: printEmojis,
          printTime: printTime,
          noBoxingByDefault: noBoxingByDefault,
        );

  @override
  String stringifyMessage(message) {
    var msg = super.stringifyMessage(message);
    var i = 0;
    var len = warpLen;
    var newStr = "";
    while (msg.length > i + len) {
      var next = i + len;
      var last = msg.indexOf("\n", i);
      if (last < i + 1 || last > next) {
        newStr += "${msg.substring(i, next)}\n";
        i = next;
      } else {
        newStr += msg.substring(i, last);
        i = last;
      }
    }
    if (i + len > msg.length) {
      newStr += msg.substring(i);
    }
    return newStr;
  }
}

loggerHttp(String text, {String key = ""}) {
  if (kDebugMode) {
    if (Platform.isIOS) {
      logger(text, key: key);
    } else {
      _logger.w("${key.isNotEmpty ? '$key ' : ''} $text");
    }
  }
  writeToFile(text);
}

loggerJson(String text) {
  if (kDebugMode) {
    if (Platform.isIOS) {
      logger(text, key: "HTTP-LOG");
    } else {
      final object = json.decode(text);
      final prettyString = const JsonEncoder.withIndent('  ').convert(object);
      _logger.w(prettyString);
    }
  }
  writeToFile(text);
}

logger(String text, {String key = ""}) {
  if (kDebugMode) {
    int singleLen = 300;
    for (int i = 0; i < (text.length / singleLen).ceil(); i++) {
      print("$key ${text.substring(i * singleLen, min(singleLen * (i + 1), text.length))}");
    }
  }
  // writeToFile(text);
}

String logDir = "";

writeToFile(String text) async {
  if (HHttp.httpEnv == HttpEnv.prod) {
    return;
  }
  try {
    if (isEmpty(logDir)) {
      Directory? directory;
      if (Platform.isAndroid) {
        directory = await getExternalStorageDirectory();
      } else {
        directory = await getApplicationDocumentsDirectory();
      }
      directory ??= await getDownloadsDirectory();
      directory ??= await getTemporaryDirectory();
      logDir = directory.path;
      if (kDebugMode) {
        print("logger directory: $logDir");
      }
    }
    final File file = File('$logDir/logger/mooeli_log_${formatTimestampToHour()}.txt');
    if (!file.existsSync()) {
      file.createSync(recursive: true);
    }
    IOSink sink = file.openWrite(mode: FileMode.append);
    sink.write("\n$text\n");
    await sink.flush();
    await sink.close();
  } catch (ex) {
    if (kDebugMode) {
      print("logger exception: $ex");
    }
  }
}

Map getStaticConfigJson() {
  try {
    String configStr = Global.sharedPrefs.getString("config_json") ?? "";
    logger("config load: $configStr");
    Map map = jsonDecode(configStr)[lang];
    return map;
  } catch (ex) {
    logger("config error: $ex");
    return {};
  }
}

String wechatCodeImageUrl = "${HHttp.getOfficialHost()}lyoral/lyoral_wechat_code.png";

saveWechatCodeImage() {
  HHttp.downFileIfNotExist(
    wechatCodeImageUrl,
    (path) async {
      await Global.saveImageFileFromCache(path);
    },
    forceDownload: true,
  );
}

sendEventPoint(String domain, String operation, [Map? param]) {
  try {
    param ??= {};
    param["domainName"] = domain;
    param["operationName"] = operation;
    param["userId"] = User.instance.getUserData("UserId");
    param["device"] = "${Global.deviceInfo}";
    logger("add event: $param");
    // addEventTrace(param);
  } catch (ex) {
    //
  }
}

String formatTimestampToHour() {
  DateTime date = DateTime.now();
  String formattedDate = DateFormat('yyyy_MM_dd_HH_mm').format(date);
  return formattedDate.substring(0, formattedDate.length - 1);
}

///无序比较
bool listEqual(List l1, List l2) {
  if (l1.length != l2.length) {
    return false;
  }
  for (dynamic tag in l1) {
    if (!l2.contains(tag)) {
      return false;
    }
  }
  return true;
}

isEmpty(param) {
  return param == null || param.isEmpty;
}

isNotEmpty(param) {
  return param != null && param.isNotEmpty;
}

allNotEmpty(List list) {
  for (dynamic param in list) {
    if (isEmpty(param)) {
      return false;
    }
  }
  return true;
}

hasNotEmpty(List list) {
  for (dynamic param in list) {
    if (isNotEmpty(param)) {
      return true;
    }
  }
  return false;
}

showCustomDialog(
  Widget customView, {
  Alignment alignment = Alignment.center,
  bool clickBackDismiss = true,
  bool useSystem = true,
  dynamic onDismiss,
      String? tag,
}) {
  SmartDialog.show(
    alignment: alignment,
    animationType: SmartAnimationType.centerScale_otherSlide,
    clickMaskDismiss: clickBackDismiss,
    useSystem: useSystem,
    tag: tag,
    onDismiss: () {
      if (onDismiss != null) {
        onDismiss();
      }
    },
    builder: (context) {
      return customView;
    },
  );
}

toast(String text) {
  SmartDialog.showToast(text, alignment: Alignment.center);
}

void showBottomWidgetsDialog(BuildContext context, String title, List<Widget> list, Function onItem) async {
  await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          width: 1.sw,
          constraints: BoxConstraints(maxHeight: 0.7.sh),
          padding: EdgeInsets.only(top: 16.sp, bottom: 20.sp),
          decoration: BoxDecoration(
            color: colorBg,
            borderRadius: BorderRadius.circular(16.sp),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.fromLTRB(20.sp, 0, 20.sp, 16.sp),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(width: 20.sp),
                    MyText(title, color2B, 16.sp, FontWeight.w500),
                    GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: Image.asset("res/imgs/icon_close.png", width: 20.sp),
                    ),
                  ],
                ),
              ),
              ...list.map(
                (e) => Click(
                  child: e,
                  onTap: () {
                    Navigator.of(context).pop();
                    onItem(list.indexOf(e));
                  },
                ),
              ),
            ],
          ),
        );
      });
}

void showBottomListDialog(BuildContext context, List<String> list, Function onItem) async {
  if (!list.contains(Lang.cancel)) {
    list.add(Lang.cancel);
  }

  double maxHeight = 50.sp * (list.length);
  if (maxHeight > 0.8.sh) {
    maxHeight = 0.8.sh;
  }

  await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        //56
        return Container(
          constraints: BoxConstraints(maxHeight: maxHeight),
          child: ListView.separated(
            physics: const NeverScrollableScrollPhysics(),
            itemCount: list.length,
            separatorBuilder: (context, index) {
              return const Divider(height: 1, thickness: 1, color: Color(0xffefefef));
            },
            itemBuilder: (context, index) {
              return Click(
                child: Container(
                  color: Colors.white,
                  width: double.infinity,
                  margin: index == list.length - 1 ? EdgeInsets.only(top: 8.sp) : EdgeInsets.zero,
                  height: index == list.length - 1 ? 42.sp : 50.sp,
                  alignment: Alignment.center,
                  child: Text(
                    list[index],
                    style: TextStyle(fontSize: 32.sp, color: index == list.length - 1 ? Colors.black38 : color2B),
                  ),
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  if (index < list.length - 1) {
                    onItem(index);
                  }
                },
              );
            },
          ),
        );
      });
}

String getJsonString(Map? json, String key) {
  if (json == null) {
    return "";
  }
  String value = json[key] ?? "";
  return value.trim();
}

int getJsonInt(Map? json, String key) {
  if (json == null) {
    return 0;
  }
  int? value = json[key];
  return value ?? 0;
}

double getJsonDouble(Map? json, String key) {
  if (json == null) {
    return 0.0;
  }
  double? value = json[key];
  return value ?? 0.0;
}

bool getJsonBool(Map? json, String key, {bool defaultValue = false}) {
  if (json == null) {
    return defaultValue;
  }
  bool? value = json[key];
  return value ?? defaultValue;
}

List getJsonList(Map? json, String key) {
  List list = [];
  if (json == null) {
    return list;
  }
  List? value = json[key];
  list.addAll(value ?? []);
  return list;
}

Map getJsonMap(Map? json, String key) {
  if (json == null) {
    return {};
  }
  Map? value = json[key];
  return value ?? {};
}

getLanguageMap() {
  return {
    Lang.language_auto: null,
    "简体中文": const Locale.fromSubtags(languageCode: 'zh'),
    "English": const Locale.fromSubtags(languageCode: 'en'),
  };
}
